{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: `${API_BASE_URL}/api`,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests\napi.interceptors.request.use((config) => {\n  if (typeof window !== 'undefined') {\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n  }\n  return config;\n});\n\n// Handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401 && typeof window !== 'undefined') {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authApi = {\n  login: (email: string, password: string) =>\n    api.post('/auth/login', { email, password }),\n  register: (userData: any) =>\n    api.post('/auth/register', userData),\n  verify: () =>\n    api.get('/auth/verify'),\n};\n\n// Products API\nexport const productsApi = {\n  getAll: (params?: any) =>\n    api.get('/products', { params }),\n  getById: (id: string) =>\n    api.get(`/products/${id}`),\n  create: (data: any) =>\n    api.post('/products', data),\n  update: (id: string, data: any) =>\n    api.put(`/products/${id}`, data),\n  getByBarcode: (barcode: string) =>\n    api.get(`/products/barcode/${barcode}`),\n};\n\n// Inventory API\nexport const inventoryApi = {\n  getAll: (params?: any) =>\n    api.get('/inventory', { params }),\n  getByProduct: (productId: string, warehouseId: string) =>\n    api.get(`/inventory/${productId}/${warehouseId}`),\n  update: (productId: string, warehouseId: string, data: any) =>\n    api.put(`/inventory/${productId}/${warehouseId}/update`, data),\n  adjust: (productId: string, warehouseId: string, data: any) =>\n    api.post(`/inventory/${productId}/${warehouseId}/adjust`, data),\n  getLowStock: () =>\n    api.get('/inventory/low-stock'),\n};\n\n// Orders API\nexport const ordersApi = {\n  getAll: (params?: any) =>\n    api.get('/orders', { params }),\n  getById: (id: string) =>\n    api.get(`/orders/${id}`),\n  create: (data: any) =>\n    api.post('/orders', data),\n  updateStatus: (id: string, status: string, notes?: string) =>\n    api.put(`/orders/${id}/status`, { status, notes }),\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  getStats: () =>\n    api.get('/dashboard/stats'),\n};\n\n// Categories API\nexport const categoriesApi = {\n  getAll: () =>\n    api.get('/categories'),\n  create: (data: any) =>\n    api.post('/categories', data),\n};\n\n// Suppliers API\nexport const suppliersApi = {\n  getAll: () =>\n    api.get('/suppliers'),\n  create: (data: any) =>\n    api.post('/suppliers', data),\n};\n\n// Warehouses API\nexport const warehousesApi = {\n  getAll: () =>\n    api.get('/warehouses'),\n  create: (data: any) =>\n    api.post('/warehouses', data),\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;AAEqB;AAFrB;;AAEA,MAAM,eAAe,6DAAmC;AAExD,wBAAwB;AACxB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS,AAAC,GAAe,OAAb,cAAa;IACzB,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,6BAA6B;AAC7B,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC5B,wCAAmC;QACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,OAAO,OAAO,CAAC,aAAa,GAAG,AAAC,UAAe,OAAN;QAC3C;IACF;IACA,OAAO;AACT;AAEA,qBAAqB;AACrB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;QACK;IAAJ,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,OAAO,aAAkB,aAAa;QACnE,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,OAAO,CAAC,OAAe,WACrB,IAAI,IAAI,CAAC,eAAe;YAAE;YAAO;QAAS;IAC5C,UAAU,CAAC,WACT,IAAI,IAAI,CAAC,kBAAkB;IAC7B,QAAQ,IACN,IAAI,GAAG,CAAC;AACZ;AAGO,MAAM,cAAc;IACzB,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,aAAa;YAAE;QAAO;IAChC,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,AAAC,aAAe,OAAH;IACvB,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,aAAa;IACxB,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,AAAC,aAAe,OAAH,KAAM;IAC7B,cAAc,CAAC,UACb,IAAI,GAAG,CAAC,AAAC,qBAA4B,OAAR;AACjC;AAGO,MAAM,eAAe;IAC1B,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,cAAc;YAAE;QAAO;IACjC,cAAc,CAAC,WAAmB,cAChC,IAAI,GAAG,CAAC,AAAC,cAA0B,OAAb,WAAU,KAAe,OAAZ;IACrC,QAAQ,CAAC,WAAmB,aAAqB,OAC/C,IAAI,GAAG,CAAC,AAAC,cAA0B,OAAb,WAAU,KAAe,OAAZ,aAAY,YAAU;IAC3D,QAAQ,CAAC,WAAmB,aAAqB,OAC/C,IAAI,IAAI,CAAC,AAAC,cAA0B,OAAb,WAAU,KAAe,OAAZ,aAAY,YAAU;IAC5D,aAAa,IACX,IAAI,GAAG,CAAC;AACZ;AAGO,MAAM,YAAY;IACvB,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,WAAW;YAAE;QAAO;IAC9B,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,AAAC,WAAa,OAAH;IACrB,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,WAAW;IACtB,cAAc,CAAC,IAAY,QAAgB,QACzC,IAAI,GAAG,CAAC,AAAC,WAAa,OAAH,IAAG,YAAU;YAAE;YAAQ;QAAM;AACpD;AAGO,MAAM,eAAe;IAC1B,UAAU,IACR,IAAI,GAAG,CAAC;AACZ;AAGO,MAAM,gBAAgB;IAC3B,QAAQ,IACN,IAAI,GAAG,CAAC;IACV,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,eAAe;AAC5B;AAGO,MAAM,eAAe;IAC1B,QAAQ,IACN,IAAI,GAAG,CAAC;IACV,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,cAAc;AAC3B;AAGO,MAAM,gBAAgB;IAC3B,QAAQ,IACN,IAAI,GAAG,CAAC;IACV,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,eAAe;AAC5B;uCAEe", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/components/ui/Breadcrumb.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { ChevronRight, Home } from 'lucide-react';\n\ninterface BreadcrumbItem {\n  label: string;\n  href: string;\n}\n\ninterface BreadcrumbProps {\n  items?: BreadcrumbItem[];\n  className?: string;\n}\n\nexport default function Breadcrumb({ items, className = '' }: BreadcrumbProps) {\n  const pathname = usePathname();\n\n  // Auto-generate breadcrumbs from pathname if items not provided\n  const breadcrumbItems = items || generateBreadcrumbs(pathname);\n\n  return (\n    <nav className={`flex ${className}`} aria-label=\"Breadcrumb\">\n      <ol className=\"inline-flex items-center space-x-1 md:space-x-3\">\n        <li className=\"inline-flex items-center\">\n          <Link\n            href=\"/dashboard\"\n            className=\"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors\"\n          >\n            <Home className=\"h-4 w-4 mr-2\" />\n            Dashboard\n          </Link>\n        </li>\n        {breadcrumbItems.map((item, index) => (\n          <li key={item.href}>\n            <div className=\"flex items-center\">\n              <ChevronRight className=\"h-4 w-4 text-gray-400 mx-1\" />\n              {index === breadcrumbItems.length - 1 ? (\n                <span className=\"text-sm font-medium text-gray-500\" aria-current=\"page\">\n                  {item.label}\n                </span>\n              ) : (\n                <Link\n                  href={item.href}\n                  className=\"text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors\"\n                >\n                  {item.label}\n                </Link>\n              )}\n            </div>\n          </li>\n        ))}\n      </ol>\n    </nav>\n  );\n}\n\nfunction generateBreadcrumbs(pathname: string): BreadcrumbItem[] {\n  const segments = pathname.split('/').filter(Boolean);\n  const breadcrumbs: BreadcrumbItem[] = [];\n\n  // Skip the first segment if it's 'dashboard'\n  const startIndex = segments[0] === 'dashboard' ? 1 : 0;\n\n  for (let i = startIndex; i < segments.length; i++) {\n    const segment = segments[i];\n    const href = '/' + segments.slice(0, i + 1).join('/');\n    \n    // Convert segment to readable label\n    const label = segment\n      .split('-')\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n      .join(' ');\n\n    breadcrumbs.push({ label, href });\n  }\n\n  return breadcrumbs;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAgBe,SAAS,WAAW,KAA0C;QAA1C,EAAE,KAAK,EAAE,YAAY,EAAE,EAAmB,GAA1C;;IACjC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,kBAAkB,SAAS,oBAAoB;IAErD,qBACE,6LAAC;QAAI,WAAW,AAAC,QAAiB,OAAV;QAAa,cAAW;kBAC9C,cAAA,6LAAC;YAAG,WAAU;;8BACZ,6LAAC;oBAAG,WAAU;8BACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,sMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;gBAIpC,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC;kCACC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCACvB,UAAU,gBAAgB,MAAM,GAAG,kBAClC,6LAAC;oCAAK,WAAU;oCAAoC,gBAAa;8CAC9D,KAAK,KAAK;;;;;yDAGb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,KAAK;;;;;;;;;;;;uBAZV,KAAK,IAAI;;;;;;;;;;;;;;;;AAqB5B;GAxCwB;;QACL,qIAAA,CAAA,cAAW;;;KADN;AA0CxB,SAAS,oBAAoB,QAAgB;IAC3C,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAC5C,MAAM,cAAgC,EAAE;IAExC,6CAA6C;IAC7C,MAAM,aAAa,QAAQ,CAAC,EAAE,KAAK,cAAc,IAAI;IAErD,IAAK,IAAI,IAAI,YAAY,IAAI,SAAS,MAAM,EAAE,IAAK;QACjD,MAAM,UAAU,QAAQ,CAAC,EAAE;QAC3B,MAAM,OAAO,MAAM,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;QAEjD,oCAAoC;QACpC,MAAM,QAAQ,QACX,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;QAER,YAAY,IAAI,CAAC;YAAE;YAAO;QAAK;IACjC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/app/dashboard/inventory/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { inventoryApi } from '@/lib/api';\nimport { formatNumber, formatDate } from '@/lib/utils';\nimport { Search, Package, AlertTriangle, TrendingUp, TrendingDown } from 'lucide-react';\nimport Breadcrumb from '@/components/ui/Breadcrumb';\n\ninterface InventoryItem {\n  id: string;\n  quantity: number;\n  reservedQty: number;\n  minThreshold: number;\n  maxThreshold: number | null;\n  location: string | null;\n  product: {\n    id: string;\n    name: string;\n    sku: string;\n    barcode: string | null;\n    category: {\n      name: string;\n    };\n  };\n  warehouse: {\n    name: string;\n  };\n  updatedAt: string;\n}\n\nexport default function InventoryPage() {\n  const [inventory, setInventory] = useState<InventoryItem[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showLowStock, setShowLowStock] = useState(false);\n\n  useEffect(() => {\n    fetchInventory();\n  }, [showLowStock]);\n\n  const fetchInventory = async () => {\n    try {\n      setLoading(true);\n      const response = showLowStock \n        ? await inventoryApi.getLowStock()\n        : await inventoryApi.getAll({ search: searchTerm });\n      \n      setInventory(showLowStock ? response.data.lowStockItems : response.data.inventory);\n    } catch (error) {\n      console.error('Failed to fetch inventory:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = () => {\n    if (!showLowStock) {\n      fetchInventory();\n    }\n  };\n\n  const getStockStatus = (item: InventoryItem) => {\n    if (item.quantity <= item.minThreshold) {\n      return { status: 'low', color: 'text-red-600', bgColor: 'bg-red-50', icon: AlertTriangle };\n    }\n    if (item.maxThreshold && item.quantity >= item.maxThreshold) {\n      return { status: 'high', color: 'text-orange-600', bgColor: 'bg-orange-50', icon: TrendingUp };\n    }\n    return { status: 'normal', color: 'text-green-600', bgColor: 'bg-green-50', icon: Package };\n  };\n\n  const filteredInventory = inventory.filter(item =>\n    item.product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    item.product.sku.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <Breadcrumb />\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Inventory Management</h1>\n          <p className=\"text-gray-600\">Monitor and manage your warehouse inventory</p>\n        </div>\n        <div className=\"flex space-x-2\">\n          <Button\n            variant={showLowStock ? \"default\" : \"outline\"}\n            onClick={() => setShowLowStock(!showLowStock)}\n          >\n            <AlertTriangle className=\"mr-2 h-4 w-4\" />\n            {showLowStock ? 'Show All' : 'Low Stock Only'}\n          </Button>\n        </div>\n      </div>\n\n      {!showLowStock && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Search Inventory</CardTitle>\n            <CardDescription>Find products by name or SKU</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex space-x-2\">\n              <div className=\"relative flex-1\">\n                <Search className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                <Input\n                  placeholder=\"Search products...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10\"\n                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n                />\n              </div>\n              <Button onClick={handleSearch}>Search</Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {filteredInventory.map((item) => {\n          const stockStatus = getStockStatus(item);\n          const StatusIcon = stockStatus.icon;\n          const availableQty = item.quantity - item.reservedQty;\n\n          return (\n            <Card key={item.id} className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader className=\"pb-3\">\n                <div className=\"flex items-center justify-between\">\n                  <CardTitle className=\"text-lg\">{item.product.name}</CardTitle>\n                  <div className={`p-2 rounded-lg ${stockStatus.bgColor}`}>\n                    <StatusIcon className={`h-4 w-4 ${stockStatus.color}`} />\n                  </div>\n                </div>\n                <CardDescription>\n                  SKU: {item.product.sku} • {item.product.category.name}\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <p className=\"text-sm text-gray-500\">Total Stock</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">\n                      {formatNumber(item.quantity)}\n                    </p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-500\">Available</p>\n                    <p className=\"text-2xl font-bold text-green-600\">\n                      {formatNumber(availableQty)}\n                    </p>\n                  </div>\n                </div>\n\n                {item.reservedQty > 0 && (\n                  <div className=\"bg-yellow-50 p-3 rounded-lg\">\n                    <p className=\"text-sm text-yellow-800\">\n                      <strong>{formatNumber(item.reservedQty)}</strong> reserved for orders\n                    </p>\n                  </div>\n                )}\n\n                <div className=\"flex justify-between text-sm text-gray-500\">\n                  <span>Min: {item.minThreshold}</span>\n                  {item.maxThreshold && <span>Max: {item.maxThreshold}</span>}\n                </div>\n\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-500\">Warehouse:</span>\n                  <span className=\"font-medium\">{item.warehouse.name}</span>\n                </div>\n\n                {item.location && (\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-500\">Location:</span>\n                    <span className=\"font-medium\">{item.location}</span>\n                  </div>\n                )}\n\n                <div className=\"text-xs text-gray-400\">\n                  Last updated: {formatDate(item.updatedAt)}\n                </div>\n\n                <div className=\"flex space-x-2 pt-2\">\n                  <Button size=\"sm\" variant=\"outline\" className=\"flex-1\">\n                    <TrendingUp className=\"mr-1 h-3 w-3\" />\n                    Adjust\n                  </Button>\n                  <Button size=\"sm\" variant=\"outline\" className=\"flex-1\">\n                    <Package className=\"mr-1 h-3 w-3\" />\n                    Details\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          );\n        })}\n      </div>\n\n      {filteredInventory.length === 0 && (\n        <Card>\n          <CardContent className=\"text-center py-12\">\n            <Package className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              {showLowStock ? 'No Low Stock Items' : 'No Inventory Found'}\n            </h3>\n            <p className=\"text-gray-500\">\n              {showLowStock \n                ? 'All items are above minimum threshold levels.'\n                : 'Try adjusting your search terms or add new products to inventory.'\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AATA;;;;;;;;;AAiCe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAa;IAEjB,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,eACb,MAAM,oHAAA,CAAA,eAAY,CAAC,WAAW,KAC9B,MAAM,oHAAA,CAAA,eAAY,CAAC,MAAM,CAAC;gBAAE,QAAQ;YAAW;YAEnD,aAAa,eAAe,SAAS,IAAI,CAAC,aAAa,GAAG,SAAS,IAAI,CAAC,SAAS;QACnF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,cAAc;YACjB;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,QAAQ,IAAI,KAAK,YAAY,EAAE;YACtC,OAAO;gBAAE,QAAQ;gBAAO,OAAO;gBAAgB,SAAS;gBAAa,MAAM,2NAAA,CAAA,gBAAa;YAAC;QAC3F;QACA,IAAI,KAAK,YAAY,IAAI,KAAK,QAAQ,IAAI,KAAK,YAAY,EAAE;YAC3D,OAAO;gBAAE,QAAQ;gBAAQ,OAAO;gBAAmB,SAAS;gBAAgB,MAAM,qNAAA,CAAA,aAAU;YAAC;QAC/F;QACA,OAAO;YAAE,QAAQ;YAAU,OAAO;YAAkB,SAAS;YAAe,MAAM,2MAAA,CAAA,UAAO;QAAC;IAC5F;IAEA,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,OACzC,KAAK,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,KAAK,OAAO,CAAC,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGhE,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAU;;;;;0BACX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,eAAe,YAAY;4BACpC,SAAS,IAAM,gBAAgB,CAAC;;8CAEhC,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCACxB,eAAe,aAAa;;;;;;;;;;;;;;;;;;YAKlC,CAAC,8BACA,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;4CACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;8CAG5C,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAMvC,6LAAC;gBAAI,WAAU;0BACZ,kBAAkB,GAAG,CAAC,CAAC;oBACtB,MAAM,cAAc,eAAe;oBACnC,MAAM,aAAa,YAAY,IAAI;oBACnC,MAAM,eAAe,KAAK,QAAQ,GAAG,KAAK,WAAW;oBAErD,qBACE,6LAAC,mIAAA,CAAA,OAAI;wBAAe,WAAU;;0CAC5B,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAW,KAAK,OAAO,CAAC,IAAI;;;;;;0DACjD,6LAAC;gDAAI,WAAW,AAAC,kBAAqC,OAApB,YAAY,OAAO;0DACnD,cAAA,6LAAC;oDAAW,WAAW,AAAC,WAA4B,OAAlB,YAAY,KAAK;;;;;;;;;;;;;;;;;kDAGvD,6LAAC,mIAAA,CAAA,kBAAe;;4CAAC;4CACT,KAAK,OAAO,CAAC,GAAG;4CAAC;4CAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,IAAI;;;;;;;;;;;;;0CAGzD,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,QAAQ;;;;;;;;;;;;0DAG/B,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;;;;;;;;;;;;;;;;;;oCAKnB,KAAK,WAAW,GAAG,mBAClB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;8DAAQ,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,WAAW;;;;;;gDAAW;;;;;;;;;;;;kDAKvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAK;oDAAM,KAAK,YAAY;;;;;;;4CAC5B,KAAK,YAAY,kBAAI,6LAAC;;oDAAK;oDAAM,KAAK,YAAY;;;;;;;;;;;;;kDAGrD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAe,KAAK,SAAS,CAAC,IAAI;;;;;;;;;;;;oCAGnD,KAAK,QAAQ,kBACZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAe,KAAK,QAAQ;;;;;;;;;;;;kDAIhD,6LAAC;wCAAI,WAAU;;4CAAwB;4CACtB,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;;kDAG1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;gDAAU,WAAU;;kEAC5C,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGzC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;gDAAU,WAAU;;kEAC5C,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;uBA/DjC,KAAK,EAAE;;;;;gBAsEtB;;;;;;YAGD,kBAAkB,MAAM,KAAK,mBAC5B,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAG,WAAU;sCACX,eAAe,uBAAuB;;;;;;sCAEzC,6LAAC;4BAAE,WAAU;sCACV,eACG,kDACA;;;;;;;;;;;;;;;;;;;;;;;AAQlB;GAnMwB;KAAA", "debugId": null}}]}