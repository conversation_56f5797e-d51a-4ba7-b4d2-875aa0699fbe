// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String
  lastName  String
  role      String @default("EMPLOYEE")
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  stockMovements StockMovement[]
  orders         Order[]
  auditLogs      AuditLog[]

  @@map("users")
}

model Category {
  id          String    @id @default(uuid())
  name        String    @unique
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  products Product[]

  @@map("categories")
}

model Supplier {
  id          String  @id @default(uuid())
  name        String
  email       String?
  phone       String?
  address     String?
  contactName String?
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  products Product[]

  @@map("suppliers")
}

model Product {
  id          String  @id @default(uuid())
  sku         String  @unique
  name        String
  description String?
  barcode     String? @unique
  price       Float
  cost        Float
  weight      Float?
  dimensions  String? // JSON string for length, width, height
  imageUrl    String?
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Foreign Keys
  categoryId String
  supplierId String

  // Relations
  category       Category        @relation(fields: [categoryId], references: [id])
  supplier       Supplier        @relation(fields: [supplierId], references: [id])
  inventory      Inventory[]
  stockMovements StockMovement[]
  orderItems     OrderItem[]

  @@map("products")
}

model Warehouse {
  id        String  @id @default(uuid())
  name      String
  address   String
  isActive  Boolean @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  inventory      Inventory[]
  stockMovements StockMovement[]
  orders         Order[]

  @@map("warehouses")
}

model Inventory {
  id            String   @id @default(uuid())
  quantity      Int      @default(0)
  reservedQty   Int      @default(0)
  minThreshold  Int      @default(0)
  maxThreshold  Int?
  location      String? // Shelf/bin location
  lastCountDate DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Foreign Keys
  productId   String
  warehouseId String

  // Relations
  product   Product   @relation(fields: [productId], references: [id])
  warehouse Warehouse @relation(fields: [warehouseId], references: [id])

  @@unique([productId, warehouseId])
  @@map("inventory")
}

model StockMovement {
  id          String         @id @default(uuid())
  type        String
  quantity    Int
  reason      String?
  reference   String? // Order ID, Transfer ID, etc.
  notes       String?
  createdAt   DateTime       @default(now())

  // Foreign Keys
  productId   String
  warehouseId String
  userId      String

  // Relations
  product   Product   @relation(fields: [productId], references: [id])
  warehouse Warehouse @relation(fields: [warehouseId], references: [id])
  user      User      @relation(fields: [userId], references: [id])

  @@map("stock_movements")
}

model Order {
  id           String      @id @default(uuid())
  orderNumber  String      @unique
  status       String @default("PENDING")
  priority     String @default("NORMAL")
  customerName String
  customerEmail String?
  shippingAddress String
  totalAmount  Float
  notes        String?
  orderDate    DateTime    @default(now())
  requiredDate DateTime?
  shippedDate  DateTime?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Foreign Keys
  warehouseId String
  userId      String

  // Relations
  warehouse  Warehouse   @relation(fields: [warehouseId], references: [id])
  user       User        @relation(fields: [userId], references: [id])
  orderItems OrderItem[]

  @@map("orders")
}

model OrderItem {
  id           String  @id @default(uuid())
  quantity     Int
  unitPrice    Float
  totalPrice   Float
  pickedQty    Int     @default(0)
  createdAt    DateTime @default(now())

  // Foreign Keys
  orderId   String
  productId String

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model AuditLog {
  id        String   @id @default(uuid())
  action    String
  entity    String
  entityId  String
  oldValues String?
  newValues String?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Foreign Keys
  userId String

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

model Setting {
  id        String   @id @default(uuid())
  key       String   @unique
  value     String
  category  String   @default("general")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}

// Note: SQLite doesn't support enums, so we use strings with validation in the application layer
// Valid values:
// UserRole: "ADMIN", "MANAGER", "EMPLOYEE"
// MovementType: "IN", "OUT", "ADJUSTMENT", "TRANSFER"
// OrderStatus: "PENDING", "PROCESSING", "PICKING", "PACKED", "SHIPPED", "DELIVERED", "CANCELLED"
// Priority: "LOW", "NORMAL", "HIGH", "URGENT"
