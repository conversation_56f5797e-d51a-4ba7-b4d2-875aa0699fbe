'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';

interface BackButtonProps {
  fallbackUrl?: string;
  className?: string;
  children?: React.ReactNode;
}

export default function BackButton({ 
  fallbackUrl = '/dashboard', 
  className = '',
  children 
}: BackButtonProps) {
  const router = useRouter();

  const handleGoBack = () => {
    // Check if there's history to go back to
    if (window.history.length > 1) {
      router.back();
    } else {
      // Fallback to a specific URL if no history
      router.push(fallbackUrl);
    }
  };

  return (
    <button
      onClick={handleGoBack}
      className={`inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors ${className}`}
    >
      <ArrowLeft className="h-4 w-4 mr-2" />
      {children || 'Back'}
    </button>
  );
}
