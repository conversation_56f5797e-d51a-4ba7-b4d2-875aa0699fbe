{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/app/dashboard/settings/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Save, Settings as SettingsIcon, Bell, Shield, Database, Globe } from 'lucide-react';\n\ninterface Setting {\n  key: string;\n  value: string;\n  category: string;\n}\n\nexport default function SettingsPage() {\n  const [settings, setSettings] = useState<Setting[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n\n  useEffect(() => {\n    // Simulate loading settings\n    setTimeout(() => {\n      setSettings([\n        { key: 'company_name', value: 'Warehouse Management System', category: 'general' },\n        { key: 'currency', value: 'USD', category: 'general' },\n        { key: 'timezone', value: 'America/New_York', category: 'general' },\n        { key: 'low_stock_threshold', value: '10', category: 'inventory' },\n        { key: 'auto_reorder', value: 'false', category: 'inventory' },\n        { key: 'email_notifications', value: 'true', category: 'notifications' },\n        { key: 'sms_notifications', value: 'false', category: 'notifications' },\n      ]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const handleSave = async () => {\n    setSaving(true);\n    // Simulate saving\n    setTimeout(() => {\n      setSaving(false);\n      alert('Settings saved successfully!');\n    }, 1000);\n  };\n\n  const updateSetting = (key: string, value: string) => {\n    setSettings(prev => prev.map(setting => \n      setting.key === key ? { ...setting, value } : setting\n    ));\n  };\n\n  const getSetting = (key: string) => {\n    return settings.find(s => s.key === key)?.value || '';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Settings</h1>\n          <p className=\"text-gray-600\">Configure system preferences and options</p>\n        </div>\n        <button\n          type=\"button\"\n          onClick={handleSave}\n          disabled={saving}\n          className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2 disabled:opacity-50\"\n        >\n          <Save className=\"h-4 w-4\" />\n          <span>{saving ? 'Saving...' : 'Save Changes'}</span>\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* General Settings */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-6 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <Globe className=\"h-5 w-5 text-gray-400 mr-2\" />\n              <h2 className=\"text-lg font-medium text-gray-900\">General Settings</h2>\n            </div>\n          </div>\n          <div className=\"p-6 space-y-4\">\n            <div>\n              <label htmlFor=\"company_name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Company Name\n              </label>\n              <input\n                id=\"company_name\"\n                type=\"text\"\n                value={getSetting('company_name')}\n                onChange={(e) => updateSetting('company_name', e.target.value)}\n                placeholder=\"Enter company name\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label htmlFor=\"currency\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Currency\n              </label>\n              <select\n                id=\"currency\"\n                value={getSetting('currency')}\n                onChange={(e) => updateSetting('currency', e.target.value)}\n                title=\"Select currency\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"USD\">USD - US Dollar</option>\n                <option value=\"EUR\">EUR - Euro</option>\n                <option value=\"GBP\">GBP - British Pound</option>\n                <option value=\"CAD\">CAD - Canadian Dollar</option>\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"timezone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Timezone\n              </label>\n              <select\n                id=\"timezone\"\n                value={getSetting('timezone')}\n                onChange={(e) => updateSetting('timezone', e.target.value)}\n                title=\"Select timezone\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"America/New_York\">Eastern Time</option>\n                <option value=\"America/Chicago\">Central Time</option>\n                <option value=\"America/Denver\">Mountain Time</option>\n                <option value=\"America/Los_Angeles\">Pacific Time</option>\n                <option value=\"UTC\">UTC</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Inventory Settings */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-6 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <Database className=\"h-5 w-5 text-gray-400 mr-2\" />\n              <h2 className=\"text-lg font-medium text-gray-900\">Inventory Settings</h2>\n            </div>\n          </div>\n          <div className=\"p-6 space-y-4\">\n            <div>\n              <label htmlFor=\"low_stock_threshold\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Low Stock Threshold\n              </label>\n              <input\n                id=\"low_stock_threshold\"\n                type=\"number\"\n                value={getSetting('low_stock_threshold')}\n                onChange={(e) => updateSetting('low_stock_threshold', e.target.value)}\n                placeholder=\"Enter threshold quantity\"\n                title=\"Low stock alert threshold\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                min=\"0\"\n              />\n              <p className=\"text-sm text-gray-500 mt-1\">\n                Alert when inventory falls below this quantity\n              </p>\n            </div>\n            <div>\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={getSetting('auto_reorder') === 'true'}\n                  onChange={(e) => updateSetting('auto_reorder', e.target.checked.toString())}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">Enable Auto Reorder</span>\n              </label>\n              <p className=\"text-sm text-gray-500 mt-1 ml-6\">\n                Automatically create purchase orders when stock is low\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Notification Settings */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-6 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <Bell className=\"h-5 w-5 text-gray-400 mr-2\" />\n              <h2 className=\"text-lg font-medium text-gray-900\">Notifications</h2>\n            </div>\n          </div>\n          <div className=\"p-6 space-y-4\">\n            <div>\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={getSetting('email_notifications') === 'true'}\n                  onChange={(e) => updateSetting('email_notifications', e.target.checked.toString())}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">Email Notifications</span>\n              </label>\n              <p className=\"text-sm text-gray-500 mt-1 ml-6\">\n                Receive email alerts for important events\n              </p>\n            </div>\n            <div>\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={getSetting('sms_notifications') === 'true'}\n                  onChange={(e) => updateSetting('sms_notifications', e.target.checked.toString())}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">SMS Notifications</span>\n              </label>\n              <p className=\"text-sm text-gray-500 mt-1 ml-6\">\n                Receive text message alerts for critical events\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Security Settings */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-6 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <Shield className=\"h-5 w-5 text-gray-400 mr-2\" />\n              <h2 className=\"text-lg font-medium text-gray-900\">Security</h2>\n            </div>\n          </div>\n          <div className=\"p-6 space-y-4\">\n            <div>\n              <button\n                type=\"button\"\n                className=\"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors\"\n                onClick={() => console.log('Change password clicked')}\n              >\n                Change Password\n              </button>\n            </div>\n            <div>\n              <button\n                type=\"button\"\n                className=\"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors\"\n                onClick={() => console.log('Two-factor auth clicked')}\n              >\n                Two-Factor Authentication\n              </button>\n            </div>\n            <div>\n              <button\n                type=\"button\"\n                className=\"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors\"\n                onClick={() => console.log('API keys clicked')}\n              >\n                API Keys\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAWe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,4BAA4B;YAC5B;0CAAW;oBACT,YAAY;wBACV;4BAAE,KAAK;4BAAgB,OAAO;4BAA+B,UAAU;wBAAU;wBACjF;4BAAE,KAAK;4BAAY,OAAO;4BAAO,UAAU;wBAAU;wBACrD;4BAAE,KAAK;4BAAY,OAAO;4BAAoB,UAAU;wBAAU;wBAClE;4BAAE,KAAK;4BAAuB,OAAO;4BAAM,UAAU;wBAAY;wBACjE;4BAAE,KAAK;4BAAgB,OAAO;4BAAS,UAAU;wBAAY;wBAC7D;4BAAE,KAAK;4BAAuB,OAAO;4BAAQ,UAAU;wBAAgB;wBACvE;4BAAE,KAAK;4BAAqB,OAAO;4BAAS,UAAU;wBAAgB;qBACvE;oBACD,WAAW;gBACb;yCAAG;QACL;iCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,UAAU;QACV,kBAAkB;QAClB,WAAW;YACT,UAAU;YACV,MAAM;QACR,GAAG;IACL;IAEA,MAAM,gBAAgB,CAAC,KAAa;QAClC,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,UAC3B,QAAQ,GAAG,KAAK,MAAM;oBAAE,GAAG,OAAO;oBAAE;gBAAM,IAAI;IAElD;IAEA,MAAM,aAAa,CAAC;YACX;QAAP,OAAO,EAAA,iBAAA,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,kBAA7B,qCAAA,eAAmC,KAAK,KAAI;IACrD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAM,SAAS,cAAc;;;;;;;;;;;;;;;;;;0BAIlC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAe,WAAU;0DAA+C;;;;;;0DAGvF,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,WAAW;gDAClB,UAAU,CAAC,IAAM,cAAc,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC7D,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAGd,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAA+C;;;;;;0DAGnF,6LAAC;gDACC,IAAG;gDACH,OAAO,WAAW;gDAClB,UAAU,CAAC,IAAM,cAAc,YAAY,EAAE,MAAM,CAAC,KAAK;gDACzD,OAAM;gDACN,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,6LAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,6LAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,6LAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;;;;;;;kDAGxB,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAA+C;;;;;;0DAGnF,6LAAC;gDACC,IAAG;gDACH,OAAO,WAAW;gDAClB,UAAU,CAAC,IAAM,cAAc,YAAY,EAAE,MAAM,CAAC,KAAK;gDACzD,OAAM;gDACN,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAmB;;;;;;kEACjC,6LAAC;wDAAO,OAAM;kEAAkB;;;;;;kEAChC,6LAAC;wDAAO,OAAM;kEAAiB;;;;;;kEAC/B,6LAAC;wDAAO,OAAM;kEAAsB;;;;;;kEACpC,6LAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAsB,WAAU;0DAA+C;;;;;;0DAG9F,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,WAAW;gDAClB,UAAU,CAAC,IAAM,cAAc,uBAAuB,EAAE,MAAM,CAAC,KAAK;gDACpE,aAAY;gDACZ,OAAM;gDACN,WAAU;gDACV,KAAI;;;;;;0DAEN,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAI5C,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,WAAW,oBAAoB;wDACxC,UAAU,CAAC,IAAM,cAAc,gBAAgB,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ;wDACxE,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,6LAAC;gDAAE,WAAU;0DAAkC;;;;;;;;;;;;;;;;;;;;;;;;kCAQrD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,WAAW,2BAA2B;wDAC/C,UAAU,CAAC,IAAM,cAAc,uBAAuB,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ;wDAC/E,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,6LAAC;gDAAE,WAAU;0DAAkC;;;;;;;;;;;;kDAIjD,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,WAAW,yBAAyB;wDAC7C,UAAU,CAAC,IAAM,cAAc,qBAAqB,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ;wDAC7E,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,6LAAC;gDAAE,WAAU;0DAAkC;;;;;;;;;;;;;;;;;;;;;;;;kCAQrD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDACC,cAAA,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,QAAQ,GAAG,CAAC;sDAC5B;;;;;;;;;;;kDAIH,6LAAC;kDACC,cAAA,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,QAAQ,GAAG,CAAC;sDAC5B;;;;;;;;;;;kDAIH,6LAAC;kDACC,cAAA,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,QAAQ,GAAG,CAAC;sDAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA5PwB;KAAA", "debugId": null}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/save.js", "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/node_modules/lucide-react/src/icons/save.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z',\n      key: '1c8476',\n    },\n  ],\n  ['path', { d: 'M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7', key: '1ydtos' }],\n  ['path', { d: 'M7 3v4a1 1 0 0 0 1 1h7', key: 't51u73' }],\n];\n\n/**\n * @component @name Save\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUuMiAzYTIgMiAwIDAgMSAxLjQuNmwzLjggMy44YTIgMiAwIDAgMSAuNiAxLjRWMTlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yeiIgLz4KICA8cGF0aCBkPSJNMTcgMjF2LTdhMSAxIDAgMCAwLTEtMUg4YTEgMSAwIDAgMC0xIDF2NyIgLz4KICA8cGF0aCBkPSJNNyAzdjRhMSAxIDAgMCAwIDEgMWg3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/save\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Save = createLucideIcon('save', __iconNode);\n\nexport default Save;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 819, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/shield.js", "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/node_modules/lucide-react/src/icons/shield.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z',\n      key: 'oel41y',\n    },\n  ],\n];\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTNjMCA1LTMuNSA3LjUtNy42NiA4Ljk1YTEgMSAwIDAgMS0uNjctLjAxQzcuNSAyMC41IDQgMTggNCAxM1Y2YTEgMSAwIDAgMSAxLTFjMiAwIDQuNS0xLjIgNi4yNC0yLjcyYTEuMTcgMS4xNyAwIDAgMSAxLjUyIDBDMTQuNTEgMy44MSAxNyA1IDE5IDVhMSAxIDAgMCAxIDEgMXoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('shield', __iconNode);\n\nexport default Shield;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/database.js", "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/node_modules/lucide-react/src/icons/database.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['ellipse', { cx: '12', cy: '5', rx: '9', ry: '3', key: 'msslwz' }],\n  ['path', { d: 'M3 5V19A9 3 0 0 0 21 19V5', key: '1wlel7' }],\n  ['path', { d: 'M3 12A9 3 0 0 0 21 12', key: 'mv7ke4' }],\n];\n\n/**\n * @component @name Database\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8ZWxsaXBzZSBjeD0iMTIiIGN5PSI1IiByeD0iOSIgcnk9IjMiIC8+CiAgPHBhdGggZD0iTTMgNVYxOUE5IDMgMCAwIDAgMjEgMTlWNSIgLz4KICA8cGF0aCBkPSJNMyAxMkE5IDMgMCAwIDAgMjEgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/database\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Database = createLucideIcon('database', __iconNode);\n\nexport default Database;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAW,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1D;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/globe.js", "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/node_modules/lucide-react/src/icons/globe.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20', key: '13o1zl' }],\n  ['path', { d: 'M2 12h20', key: '9i4pu4' }],\n];\n\n/**\n * @component @name Globe\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTIgMmExNC41IDE0LjUgMCAwIDAgMCAyMCAxNC41IDE0LjUgMCAwIDAgMC0yMCIgLz4KICA8cGF0aCBkPSJNMiAxMmgyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/globe\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Globe = createLucideIcon('globe', __iconNode);\n\nexport default Globe;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChF;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}