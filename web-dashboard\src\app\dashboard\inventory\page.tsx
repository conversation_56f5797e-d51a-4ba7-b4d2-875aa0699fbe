'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { inventoryApi } from '@/lib/api';
import { formatNumber, formatDate } from '@/lib/utils';
import { Search, Package, AlertTriangle, TrendingUp, TrendingDown } from 'lucide-react';
import Breadcrumb from '@/components/ui/Breadcrumb';

interface InventoryItem {
  id: string;
  quantity: number;
  reservedQty: number;
  minThreshold: number;
  maxThreshold: number | null;
  location: string | null;
  product: {
    id: string;
    name: string;
    sku: string;
    barcode: string | null;
    category: {
      name: string;
    };
  };
  warehouse: {
    name: string;
  };
  updatedAt: string;
}

export default function InventoryPage() {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showLowStock, setShowLowStock] = useState(false);

  useEffect(() => {
    fetchInventory();
  }, [showLowStock]);

  const fetchInventory = async () => {
    try {
      setLoading(true);
      const response = showLowStock 
        ? await inventoryApi.getLowStock()
        : await inventoryApi.getAll({ search: searchTerm });
      
      setInventory(showLowStock ? response.data.lowStockItems : response.data.inventory);
    } catch (error) {
      console.error('Failed to fetch inventory:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (!showLowStock) {
      fetchInventory();
    }
  };

  const getStockStatus = (item: InventoryItem) => {
    if (item.quantity <= item.minThreshold) {
      return { status: 'low', color: 'text-red-600', bgColor: 'bg-red-50', icon: AlertTriangle };
    }
    if (item.maxThreshold && item.quantity >= item.maxThreshold) {
      return { status: 'high', color: 'text-orange-600', bgColor: 'bg-orange-50', icon: TrendingUp };
    }
    return { status: 'normal', color: 'text-green-600', bgColor: 'bg-green-50', icon: Package };
  };

  const filteredInventory = inventory.filter(item =>
    item.product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.product.sku.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Breadcrumb />
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Inventory Management</h1>
          <p className="text-gray-600">Monitor and manage your warehouse inventory</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant={showLowStock ? "default" : "outline"}
            onClick={() => setShowLowStock(!showLowStock)}
          >
            <AlertTriangle className="mr-2 h-4 w-4" />
            {showLowStock ? 'Show All' : 'Low Stock Only'}
          </Button>
        </div>
      </div>

      {!showLowStock && (
        <Card>
          <CardHeader>
            <CardTitle>Search Inventory</CardTitle>
            <CardDescription>Find products by name or SKU</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <Button onClick={handleSearch}>Search</Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredInventory.map((item) => {
          const stockStatus = getStockStatus(item);
          const StatusIcon = stockStatus.icon;
          const availableQty = item.quantity - item.reservedQty;

          return (
            <Card key={item.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{item.product.name}</CardTitle>
                  <div className={`p-2 rounded-lg ${stockStatus.bgColor}`}>
                    <StatusIcon className={`h-4 w-4 ${stockStatus.color}`} />
                  </div>
                </div>
                <CardDescription>
                  SKU: {item.product.sku} • {item.product.category.name}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Total Stock</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatNumber(item.quantity)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Available</p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatNumber(availableQty)}
                    </p>
                  </div>
                </div>

                {item.reservedQty > 0 && (
                  <div className="bg-yellow-50 p-3 rounded-lg">
                    <p className="text-sm text-yellow-800">
                      <strong>{formatNumber(item.reservedQty)}</strong> reserved for orders
                    </p>
                  </div>
                )}

                <div className="flex justify-between text-sm text-gray-500">
                  <span>Min: {item.minThreshold}</span>
                  {item.maxThreshold && <span>Max: {item.maxThreshold}</span>}
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Warehouse:</span>
                  <span className="font-medium">{item.warehouse.name}</span>
                </div>

                {item.location && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Location:</span>
                    <span className="font-medium">{item.location}</span>
                  </div>
                )}

                <div className="text-xs text-gray-400">
                  Last updated: {formatDate(item.updatedAt)}
                </div>

                <div className="flex space-x-2 pt-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    Adjust
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    <Package className="mr-1 h-3 w-3" />
                    Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredInventory.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {showLowStock ? 'No Low Stock Items' : 'No Inventory Found'}
            </h3>
            <p className="text-gray-500">
              {showLowStock 
                ? 'All items are above minimum threshold levels.'
                : 'Try adjusting your search terms or add new products to inventory.'
              }
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
