import express from 'express';
import Jo<PERSON> from 'joi';
import { PrismaClient } from '@prisma/client';
import { AuthRequest } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';
// Import will be handled via dependency injection

const router = express.Router();
const prisma = new PrismaClient();

const orderSchema = Joi.object({
  customerName: Joi.string().required(),
  customerEmail: Joi.string().email().optional(),
  shippingAddress: Joi.string().required(),
  priority: Joi.string().valid('LOW', 'NORMAL', 'HIGH', 'URGENT').default('NORMAL'),
  notes: Joi.string().optional(),
  requiredDate: Joi.date().optional(),
  warehouseId: Joi.string().required(),
  items: Joi.array().items(
    Joi.object({
      productId: Joi.string().required(),
      quantity: Joi.number().integer().positive().required(),
      unitPrice: Joi.number().positive().required()
    })
  ).min(1).required()
});

/**
 * @swagger
 * /api/orders:
 *   get:
 *     summary: Get all orders
 *     tags: [Orders]
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, PROCESSING, PICKING, PACKED, SHIPPED, DELIVERED, CANCELLED]
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [LOW, NORMAL, HIGH, URGENT]
 *       - in: query
 *         name: warehouseId
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of orders
 */
router.get('/', async (req: AuthRequest, res, next) => {
  try {
    const { status, priority, warehouseId, page = '1', limit = '50' } = req.query;
    
    const where: any = {};
    if (status) where.status = status as string;
    if (priority) where.priority = priority as string;
    if (warehouseId) where.warehouseId = warehouseId as string;

    const orders = await prisma.order.findMany({
      where,
      include: {
        warehouse: true,
        user: {
          select: {
            firstName: true,
            lastName: true
          }
        },
        orderItems: {
          include: {
            product: true
          }
        }
      },
      skip: (parseInt(page as string) - 1) * parseInt(limit as string),
      take: parseInt(limit as string),
      orderBy: [
        { priority: 'desc' },
        { orderDate: 'desc' }
      ]
    });

    const total = await prisma.order.count({ where });

    res.json({
      orders,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        pages: Math.ceil(total / parseInt(limit as string))
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/orders:
 *   post:
 *     summary: Create a new order
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerName
 *               - shippingAddress
 *               - warehouseId
 *               - items
 *             properties:
 *               customerName:
 *                 type: string
 *               customerEmail:
 *                 type: string
 *               shippingAddress:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [LOW, NORMAL, HIGH, URGENT]
 *               notes:
 *                 type: string
 *               requiredDate:
 *                 type: string
 *                 format: date-time
 *               warehouseId:
 *                 type: string
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     productId:
 *                       type: string
 *                     quantity:
 *                       type: integer
 *                     unitPrice:
 *                       type: number
 *     responses:
 *       201:
 *         description: Order created successfully
 */
router.post('/', async (req: AuthRequest, res, next) => {
  try {
    const { error, value } = orderSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const { items, ...orderData } = value;

    // Generate order number
    const orderCount = await prisma.order.count();
    const orderNumber = `ORD-${String(orderCount + 1).padStart(6, '0')}`;

    // Calculate total amount
    const totalAmount = items.reduce((sum: number, item: any) => 
      sum + (item.quantity * item.unitPrice), 0
    );

    // Create order with items in transaction
    const order = await prisma.$transaction(async (tx) => {
      // Create order
      const newOrder = await tx.order.create({
        data: {
          ...orderData,
          orderNumber,
          totalAmount,
          userId: req.user!.id
        }
      });

      // Create order items and reserve inventory
      for (const item of items) {
        await tx.orderItem.create({
          data: {
            orderId: newOrder.id,
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.quantity * item.unitPrice
          }
        });

        // Reserve inventory
        await tx.inventory.updateMany({
          where: {
            productId: item.productId,
            warehouseId: orderData.warehouseId
          },
          data: {
            reservedQty: {
              increment: item.quantity
            }
          }
        });
      }

      return newOrder;
    });

    // TODO: Emit real-time notification via WebSocket
    console.log('Order created:', {
      orderId: order.id,
      orderNumber: order.orderNumber,
      priority: order.priority
    });

    res.status(201).json({
      message: 'Order created successfully',
      order
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/orders/{id}/status:
 *   put:
 *     summary: Update order status
 *     tags: [Orders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [PENDING, PROCESSING, PICKING, PACKED, SHIPPED, DELIVERED, CANCELLED]
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Order status updated successfully
 */
/**
 * @swagger
 * /api/orders/{id}:
 *   get:
 *     summary: Get order by ID
 *     tags: [Orders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Order details
 *       404:
 *         description: Order not found
 */
router.get('/:id', async (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;

    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        warehouse: true,
        user: {
          select: {
            firstName: true,
            lastName: true
          }
        },
        orderItems: {
          include: {
            product: {
              include: {
                category: true,
                supplier: true
              }
            }
          }
        }
      }
    });

    if (!order) {
      return res.status(404).json({ error: 'Order not found' });
    }

    res.json({ order });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/orders/{id}:
 *   delete:
 *     summary: Delete order
 *     tags: [Orders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Order deleted successfully
 *       404:
 *         description: Order not found
 */
router.delete('/:id', async (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;

    // Check if order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id },
      include: {
        orderItems: true
      }
    });

    if (!existingOrder) {
      return res.status(404).json({ error: 'Order not found' });
    }

    // Don't allow deletion of shipped or delivered orders
    if (['SHIPPED', 'DELIVERED'].includes(existingOrder.status)) {
      return res.status(400).json({
        error: 'Cannot delete shipped or delivered orders'
      });
    }

    // Delete order and release reserved inventory
    await prisma.$transaction(async (tx) => {
      // Release reserved inventory
      for (const item of existingOrder.orderItems) {
        await tx.inventory.updateMany({
          where: {
            productId: item.productId,
            warehouseId: existingOrder.warehouseId
          },
          data: {
            reservedQty: {
              decrement: item.quantity
            }
          }
        });
      }

      // Delete order items first
      await tx.orderItem.deleteMany({
        where: { orderId: id }
      });

      // Delete order
      await tx.order.delete({
        where: { id }
      });
    });

    res.json({ message: 'Order deleted successfully' });
  } catch (error) {
    next(error);
  }
});

router.put('/:id/status', async (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    const { status, notes } = req.body;

    const validStatuses = ['PENDING', 'PROCESSING', 'PICKING', 'PACKED', 'SHIPPED', 'DELIVERED', 'CANCELLED'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    const updateData: any = { status };
    if (status === 'SHIPPED') {
      updateData.shippedDate = new Date();
    }

    const order = await prisma.order.update({
      where: { id },
      data: updateData,
      include: {
        orderItems: {
          include: {
            product: true
          }
        }
      }
    });

    // If order is cancelled, release reserved inventory
    if (status === 'CANCELLED') {
      await prisma.$transaction(async (tx) => {
        for (const item of order.orderItems) {
          await tx.inventory.updateMany({
            where: {
              productId: item.productId,
              warehouseId: order.warehouseId
            },
            data: {
              reservedQty: {
                decrement: item.quantity
              }
            }
          });
        }
      });
    }

    // TODO: Emit real-time update via WebSocket
    console.log('Order status updated:', {
      orderId: order.id,
      orderNumber: order.orderNumber,
      status: order.status
    });

    res.json({
      message: 'Order status updated successfully',
      order
    });
  } catch (error) {
    next(error);
  }
});

export default router;
