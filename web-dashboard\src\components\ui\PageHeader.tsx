'use client';

import { ReactNode } from 'react';
import Breadcrumb from './Breadcrumb';
import BackButton from './BackButton';

interface PageHeaderProps {
  title: string;
  description?: string;
  showBreadcrumb?: boolean;
  showBackButton?: boolean;
  backButtonFallback?: string;
  actions?: ReactNode;
  className?: string;
}

export default function PageHeader({
  title,
  description,
  showBreadcrumb = true,
  showBackButton = false,
  backButtonFallback = '/dashboard',
  actions,
  className = ''
}: PageHeaderProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      {showBreadcrumb && <Breadcrumb />}
      
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-start gap-4">
          {showBackButton && (
            <div className="flex-shrink-0 pt-1">
              <BackButton fallbackUrl={backButtonFallback} />
            </div>
          )}
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">{title}</h1>
            {description && (
              <p className="mt-1 text-sm sm:text-base text-gray-600">{description}</p>
            )}
          </div>
        </div>
        
        {actions && (
          <div className="flex-shrink-0">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
}
