{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  // Use a consistent locale and timezone to prevent hydration mismatches\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    timeZone: 'UTC', // Use UTC to ensure consistency\n  }).format(new Date(date))\n}\n\nexport function formatDateTime(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n    timeZone: 'UTC', // Use UTC to ensure consistency\n  }).format(new Date(date))\n}\n\nexport function formatNumber(num: number): string {\n  return new Intl.NumberFormat('en-US').format(num)\n}\n\nexport function getErrorMessage(error: any): string {\n  if (error.response?.data?.error) {\n    return error.response.data.error;\n  }\n  if (error.message) {\n    return error.message;\n  }\n  return 'An unexpected error occurred';\n}\n\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    // Order statuses\n    PENDING: 'bg-yellow-100 text-yellow-800',\n    PROCESSING: 'bg-blue-100 text-blue-800',\n    PICKING: 'bg-purple-100 text-purple-800',\n    PACKED: 'bg-indigo-100 text-indigo-800',\n    SHIPPED: 'bg-green-100 text-green-800',\n    DELIVERED: 'bg-green-100 text-green-800',\n    CANCELLED: 'bg-red-100 text-red-800',\n\n    // Priority levels\n    LOW: 'bg-gray-100 text-gray-800',\n    NORMAL: 'bg-blue-100 text-blue-800',\n    HIGH: 'bg-orange-100 text-orange-800',\n    URGENT: 'bg-red-100 text-red-800',\n\n    // User roles\n    ADMIN: 'bg-red-100 text-red-800',\n    MANAGER: 'bg-blue-100 text-blue-800',\n    EMPLOYEE: 'bg-green-100 text-green-800',\n\n    // General statuses\n    ACTIVE: 'bg-green-100 text-green-800',\n    INACTIVE: 'bg-red-100 text-red-800',\n  };\n\n  return statusColors[status.toUpperCase()] || 'bg-gray-100 text-gray-800';\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,uEAAuE;IACvE,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,UAAU;IACZ,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,UAAU;IACZ,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,gBAAgB,KAAU;QACpC,sBAAA;IAAJ,KAAI,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,KAAK,EAAE;QAC/B,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK;IAClC;IACA,IAAI,MAAM,OAAO,EAAE;QACjB,OAAO,MAAM,OAAO;IACtB;IACA,OAAO;AACT;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,iBAAiB;QACjB,SAAS;QACT,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,SAAS;QACT,WAAW;QACX,WAAW;QAEX,kBAAkB;QAClB,KAAK;QACL,QAAQ;QACR,MAAM;QACN,QAAQ;QAER,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;QAEV,mBAAmB;QACnB,QAAQ;QACR,UAAU;IACZ;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: `${API_BASE_URL}/api`,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests\napi.interceptors.request.use((config) => {\n  if (typeof window !== 'undefined') {\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n  }\n  return config;\n});\n\n// Handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401 && typeof window !== 'undefined') {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authApi = {\n  login: (email: string, password: string) =>\n    api.post('/auth/login', { email, password }),\n  register: (userData: any) =>\n    api.post('/auth/register', userData),\n  verify: () =>\n    api.get('/auth/verify'),\n};\n\n// Products API\nexport const productsApi = {\n  getAll: (params?: any) =>\n    api.get('/products', { params }),\n  getById: (id: string) =>\n    api.get(`/products/${id}`),\n  create: (data: any) =>\n    api.post('/products', data),\n  update: (id: string, data: any) =>\n    api.put(`/products/${id}`, data),\n  delete: (id: string) =>\n    api.delete(`/products/${id}`),\n  getByBarcode: (barcode: string) =>\n    api.get(`/products/barcode/${barcode}`),\n};\n\n// Inventory API\nexport const inventoryApi = {\n  getAll: (params?: any) =>\n    api.get('/inventory', { params }),\n  getByProduct: (productId: string, warehouseId: string) =>\n    api.get(`/inventory/${productId}/${warehouseId}`),\n  update: (productId: string, warehouseId: string, data: any) =>\n    api.put(`/inventory/${productId}/${warehouseId}/update`, data),\n  adjust: (productId: string, warehouseId: string, data: any) =>\n    api.post(`/inventory/${productId}/${warehouseId}/adjust`, data),\n  getLowStock: () =>\n    api.get('/inventory/low-stock'),\n};\n\n// Orders API\nexport const ordersApi = {\n  getAll: (params?: any) =>\n    api.get('/orders', { params }),\n  getById: (id: string) =>\n    api.get(`/orders/${id}`),\n  create: (data: any) =>\n    api.post('/orders', data),\n  update: (id: string, data: any) =>\n    api.put(`/orders/${id}`, data),\n  delete: (id: string) =>\n    api.delete(`/orders/${id}`),\n  updateStatus: (id: string, status: string, notes?: string) =>\n    api.put(`/orders/${id}/status`, { status, notes }),\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  getStats: () =>\n    api.get('/dashboard/stats'),\n};\n\n// Categories API\nexport const categoriesApi = {\n  getAll: () =>\n    api.get('/categories'),\n  getById: (id: string) =>\n    api.get(`/categories/${id}`),\n  create: (data: any) =>\n    api.post('/categories', data),\n  update: (id: string, data: any) =>\n    api.put(`/categories/${id}`, data),\n  delete: (id: string) =>\n    api.delete(`/categories/${id}`),\n};\n\n// Suppliers API\nexport const suppliersApi = {\n  getAll: () =>\n    api.get('/suppliers'),\n  getById: (id: string) =>\n    api.get(`/suppliers/${id}`),\n  create: (data: any) =>\n    api.post('/suppliers', data),\n  update: (id: string, data: any) =>\n    api.put(`/suppliers/${id}`, data),\n  delete: (id: string) =>\n    api.delete(`/suppliers/${id}`),\n};\n\n// Warehouses API\nexport const warehousesApi = {\n  getAll: () =>\n    api.get('/warehouses'),\n  getById: (id: string) =>\n    api.get(`/warehouses/${id}`),\n  create: (data: any) =>\n    api.post('/warehouses', data),\n  update: (id: string, data: any) =>\n    api.put(`/warehouses/${id}`, data),\n  delete: (id: string) =>\n    api.delete(`/warehouses/${id}`),\n};\n\n// Users API\nexport const usersApi = {\n  getAll: (params?: any) =>\n    api.get('/users', { params }),\n  getById: (id: string) =>\n    api.get(`/users/${id}`),\n  create: (data: any) =>\n    api.post('/users', data),\n  update: (id: string, data: any) =>\n    api.put(`/users/${id}`, data),\n  delete: (id: string) =>\n    api.delete(`/users/${id}`),\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;;AAEqB;AAFrB;;AAEA,MAAM,eAAe,6DAAmC;AAExD,wBAAwB;AACxB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS,AAAC,GAAe,OAAb,cAAa;IACzB,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,6BAA6B;AAC7B,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC5B,wCAAmC;QACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,OAAO,OAAO,CAAC,aAAa,GAAG,AAAC,UAAe,OAAN;QAC3C;IACF;IACA,OAAO;AACT;AAEA,qBAAqB;AACrB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;QACK;IAAJ,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,OAAO,aAAkB,aAAa;QACnE,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,OAAO,CAAC,OAAe,WACrB,IAAI,IAAI,CAAC,eAAe;YAAE;YAAO;QAAS;IAC5C,UAAU,CAAC,WACT,IAAI,IAAI,CAAC,kBAAkB;IAC7B,QAAQ,IACN,IAAI,GAAG,CAAC;AACZ;AAGO,MAAM,cAAc;IACzB,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,aAAa;YAAE;QAAO;IAChC,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,AAAC,aAAe,OAAH;IACvB,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,aAAa;IACxB,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,AAAC,aAAe,OAAH,KAAM;IAC7B,QAAQ,CAAC,KACP,IAAI,MAAM,CAAC,AAAC,aAAe,OAAH;IAC1B,cAAc,CAAC,UACb,IAAI,GAAG,CAAC,AAAC,qBAA4B,OAAR;AACjC;AAGO,MAAM,eAAe;IAC1B,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,cAAc;YAAE;QAAO;IACjC,cAAc,CAAC,WAAmB,cAChC,IAAI,GAAG,CAAC,AAAC,cAA0B,OAAb,WAAU,KAAe,OAAZ;IACrC,QAAQ,CAAC,WAAmB,aAAqB,OAC/C,IAAI,GAAG,CAAC,AAAC,cAA0B,OAAb,WAAU,KAAe,OAAZ,aAAY,YAAU;IAC3D,QAAQ,CAAC,WAAmB,aAAqB,OAC/C,IAAI,IAAI,CAAC,AAAC,cAA0B,OAAb,WAAU,KAAe,OAAZ,aAAY,YAAU;IAC5D,aAAa,IACX,IAAI,GAAG,CAAC;AACZ;AAGO,MAAM,YAAY;IACvB,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,WAAW;YAAE;QAAO;IAC9B,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,AAAC,WAAa,OAAH;IACrB,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,WAAW;IACtB,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,AAAC,WAAa,OAAH,KAAM;IAC3B,QAAQ,CAAC,KACP,IAAI,MAAM,CAAC,AAAC,WAAa,OAAH;IACxB,cAAc,CAAC,IAAY,QAAgB,QACzC,IAAI,GAAG,CAAC,AAAC,WAAa,OAAH,IAAG,YAAU;YAAE;YAAQ;QAAM;AACpD;AAGO,MAAM,eAAe;IAC1B,UAAU,IACR,IAAI,GAAG,CAAC;AACZ;AAGO,MAAM,gBAAgB;IAC3B,QAAQ,IACN,IAAI,GAAG,CAAC;IACV,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,AAAC,eAAiB,OAAH;IACzB,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,eAAe;IAC1B,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,AAAC,eAAiB,OAAH,KAAM;IAC/B,QAAQ,CAAC,KACP,IAAI,MAAM,CAAC,AAAC,eAAiB,OAAH;AAC9B;AAGO,MAAM,eAAe;IAC1B,QAAQ,IACN,IAAI,GAAG,CAAC;IACV,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,AAAC,cAAgB,OAAH;IACxB,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,cAAc;IACzB,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,AAAC,cAAgB,OAAH,KAAM;IAC9B,QAAQ,CAAC,KACP,IAAI,MAAM,CAAC,AAAC,cAAgB,OAAH;AAC7B;AAGO,MAAM,gBAAgB;IAC3B,QAAQ,IACN,IAAI,GAAG,CAAC;IACV,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,AAAC,eAAiB,OAAH;IACzB,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,eAAe;IAC1B,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,AAAC,eAAiB,OAAH,KAAM;IAC/B,QAAQ,CAAC,KACP,IAAI,MAAM,CAAC,AAAC,eAAiB,OAAH;AAC9B;AAGO,MAAM,WAAW;IACtB,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,UAAU;YAAE;QAAO;IAC7B,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,AAAC,UAAY,OAAH;IACpB,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,UAAU;IACrB,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,AAAC,UAAY,OAAH,KAAM;IAC1B,QAAQ,CAAC,KACP,IAAI,MAAM,CAAC,AAAC,UAAY,OAAH;AACzB;uCAEe", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { authApi } from '@/lib/api';\nimport { useAuthStore } from '@/store/auth';\nimport { Warehouse, Lock, Mail } from 'lucide-react';\n\nconst loginSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n});\n\ntype LoginForm = z.infer<typeof loginSchema>;\n\nexport default function LoginPage() {\n  const router = useRouter();\n  const { login, isAuthenticated, isHydrated } = useAuthStore();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Initialize form hooks BEFORE any conditional returns\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<LoginForm>({\n    resolver: zodResolver(loginSchema),\n  });\n\n  // Redirect to dashboard if already authenticated\n  useEffect(() => {\n    if (isHydrated && isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, isHydrated, router]);\n\n  // Show loading state during hydration\n  if (!isHydrated) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  // Don't render login form if already authenticated (will redirect)\n  if (isAuthenticated) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  const onSubmit = async (data: LoginForm) => {\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const response = await authApi.login(data.email, data.password);\n      const { user, token } = response.data;\n      \n      login(user, token);\n      router.push('/dashboard');\n    } catch (err: any) {\n      setError(err.response?.data?.error || 'Login failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          <div className=\"flex justify-center\">\n            <Warehouse className=\"h-12 w-12 text-blue-600\" />\n          </div>\n          <h2 className=\"mt-6 text-3xl font-extrabold text-gray-900\">\n            Warehouse Management\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Sign in to your account\n          </p>\n        </div>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Sign In</CardTitle>\n            <CardDescription>\n              Enter your credentials to access the warehouse management system\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n              {error && (\n                <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm\">\n                  {error}\n                </div>\n              )}\n\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Email Address\n                </label>\n                <div className=\"relative\">\n                  <Mail className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                  <Input\n                    {...register('email')}\n                    type=\"email\"\n                    placeholder=\"Enter your email\"\n                    className=\"pl-10\"\n                  />\n                </div>\n                {errors.email && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Password\n                </label>\n                <div className=\"relative\">\n                  <Lock className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                  <Input\n                    {...register('password')}\n                    type=\"password\"\n                    placeholder=\"Enter your password\"\n                    className=\"pl-10\"\n                  />\n                </div>\n                {errors.password && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.password.message}</p>\n                )}\n              </div>\n\n              <Button\n                type=\"submit\"\n                className=\"w-full\"\n                disabled={isLoading}\n              >\n                {isLoading ? 'Signing in...' : 'Sign In'}\n              </Button>\n            </form>\n\n            <div className=\"mt-6 text-center\">\n              <p className=\"text-sm text-gray-600\">\n                Demo credentials: <EMAIL> / password123\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AAZA;;;;;;;;;;;;AAcA,MAAM,cAAc,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,uDAAuD;IACvD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAa;QACrB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,cAAc,iBAAiB;gBACjC,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC;QAAiB;QAAY;KAAO;IAExC,sCAAsC;IACtC,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,mEAAmE;IACnE,IAAI,iBAAiB;QACnB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC,KAAK,KAAK,EAAE,KAAK,QAAQ;YAC9D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;YAErC,MAAM,MAAM;YACZ,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAU;gBACR,oBAAA;YAAT,SAAS,EAAA,gBAAA,IAAI,QAAQ,cAAZ,qCAAA,qBAAA,cAAc,IAAI,cAAlB,yCAAA,mBAAoB,KAAK,KAAI;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,6LAAC;4BAAG,WAAU;sCAA6C;;;;;;sCAG3D,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6LAAC,mIAAA,CAAA,cAAW;;8CACV,6LAAC;oCAAK,UAAU,aAAa;oCAAW,WAAU;;wCAC/C,uBACC,6LAAC;4CAAI,WAAU;sDACZ;;;;;;sDAIL,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA+C;;;;;;8DAGhF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC,oIAAA,CAAA,QAAK;4DACH,GAAG,SAAS,QAAQ;4DACrB,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;;;;;;;gDAGb,OAAO,KAAK,kBACX,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;sDAIlE,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA+C;;;;;;8DAGnF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC,oIAAA,CAAA,QAAK;4DACH,GAAG,SAAS,WAAW;4DACxB,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;;;;;;;gDAGb,OAAO,QAAQ,kBACd,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;sDAIrE,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU;sDAET,YAAY,kBAAkB;;;;;;;;;;;;8CAInC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;GA9IwB;;QACP,qIAAA,CAAA,YAAS;QACuB,uHAAA,CAAA,eAAY;QASvD,iKAAA,CAAA,UAAO;;;KAXW", "debugId": null}}]}