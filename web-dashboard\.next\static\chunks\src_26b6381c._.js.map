{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/store/auth.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist, createJSONStorage } from 'zustand/middleware';\n\ninterface User {\n  id: string;\n  email: string;\n  username: string;\n  firstName: string;\n  lastName: string;\n  role: string;\n}\n\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isHydrated: boolean;\n  login: (user: User, token: string) => void;\n  logout: () => void;\n  updateUser: (user: Partial<User>) => void;\n  setHydrated: () => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isHydrated: false,\n      login: (user, token) => {\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('auth_token', token);\n        }\n        set({ user, token, isAuthenticated: true });\n      },\n      logout: () => {\n        if (typeof window !== 'undefined') {\n          localStorage.removeItem('auth_token');\n        }\n        set({ user: null, token: null, isAuthenticated: false });\n      },\n      updateUser: (userData) =>\n        set((state) => ({\n          user: state.user ? { ...state.user, ...userData } : null,\n        })),\n      setHydrated: () => set({ isHydrated: true }),\n    }),\n    {\n      name: 'auth-storage',\n      storage: createJSONStorage(() =>\n        typeof window !== 'undefined' ? localStorage : {\n          getItem: () => null,\n          setItem: () => {},\n          removeItem: () => {},\n        }\n      ),\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n        isAuthenticated: state.isAuthenticated,\n      }),\n      onRehydrateStorage: () => (state) => {\n        state?.setHydrated();\n      },\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAsBO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,OAAO;QACP,iBAAiB;QACjB,YAAY;QACZ,OAAO,CAAC,MAAM;YACZ,wCAAmC;gBACjC,aAAa,OAAO,CAAC,cAAc;YACrC;YACA,IAAI;gBAAE;gBAAM;gBAAO,iBAAiB;YAAK;QAC3C;QACA,QAAQ;YACN,wCAAmC;gBACjC,aAAa,UAAU,CAAC;YAC1B;YACA,IAAI;gBAAE,MAAM;gBAAM,OAAO;gBAAM,iBAAiB;YAAM;QACxD;QACA,YAAY,CAAC,WACX,IAAI,CAAC,QAAU,CAAC;oBACd,MAAM,MAAM,IAAI,GAAG;wBAAE,GAAG,MAAM,IAAI;wBAAE,GAAG,QAAQ;oBAAC,IAAI;gBACtD,CAAC;QACH,aAAa,IAAM,IAAI;gBAAE,YAAY;YAAK;IAC5C,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE,IACzB,uCAAgC,eAAe;IAMjD,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,OAAO,MAAM,KAAK;YAClB,iBAAiB,MAAM,eAAe;QACxC,CAAC;IACD,oBAAoB,IAAM,CAAC;YACzB,kBAAA,4BAAA,MAAO,WAAW;QACpB;AACF", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/lib/socket.ts"], "sourcesContent": ["import { io, Socket } from 'socket.io-client';\n\nconst SOCKET_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001';\n\nclass SocketManager {\n  private socket: Socket | null = null;\n  private listeners: Map<string, Function[]> = new Map();\n\n  connect() {\n    if (typeof window === 'undefined') return; // Skip on server\n    if (this.socket?.connected) return;\n\n    this.socket = io(SOCKET_URL, {\n      transports: ['websocket'],\n      autoConnect: true,\n    });\n\n    this.socket.on('connect', () => {\n      console.log('Connected to server');\n    });\n\n    this.socket.on('disconnect', () => {\n      console.log('Disconnected from server');\n    });\n\n    // Re-register all listeners\n    this.listeners.forEach((callbacks, event) => {\n      callbacks.forEach(callback => {\n        this.socket?.on(event, callback);\n      });\n    });\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n    }\n  }\n\n  on(event: string, callback: Function) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, []);\n    }\n    this.listeners.get(event)?.push(callback);\n\n    if (this.socket) {\n      this.socket.on(event, callback as any);\n    }\n  }\n\n  off(event: string, callback?: Function) {\n    if (callback) {\n      const callbacks = this.listeners.get(event);\n      if (callbacks) {\n        const index = callbacks.indexOf(callback);\n        if (index > -1) {\n          callbacks.splice(index, 1);\n        }\n      }\n      this.socket?.off(event, callback as any);\n    } else {\n      this.listeners.delete(event);\n      this.socket?.off(event);\n    }\n  }\n\n  emit(event: string, data?: any) {\n    this.socket?.emit(event, data);\n  }\n\n  joinWarehouse(warehouseId: string) {\n    this.emit('join:warehouse', warehouseId);\n  }\n\n  leaveWarehouse(warehouseId: string) {\n    this.emit('leave:warehouse', warehouseId);\n  }\n}\n\nexport const socketManager = new SocketManager();\nexport default socketManager;\n"], "names": [], "mappings": ";;;;AAEmB;;AAFnB;AAAA;;;AAEA,MAAM,aAAa,2DAAkC;AAErD,MAAM;IAIJ,UAAU;YAEJ;QADJ;;SAA2C,iBAAiB;QAC5D,KAAI,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,SAAS,EAAE;QAE5B,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,kLAAA,CAAA,KAAE,AAAD,EAAE,YAAY;YAC3B,YAAY;gBAAC;aAAY;YACzB,aAAa;QACf;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YACxB,QAAQ,GAAG,CAAC;QACd;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc;YAC3B,QAAQ,GAAG,CAAC;QACd;QAEA,4BAA4B;QAC5B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,WAAW;YACjC,UAAU,OAAO,CAAC,CAAA;oBAChB;iBAAA,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,EAAE,CAAC,OAAO;YACzB;QACF;IACF;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;IACF;IAEA,GAAG,KAAa,EAAE,QAAkB,EAAE;YAIpC;QAHA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ;YAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE;QAC9B;SACA,sBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAnB,0CAAA,oBAA2B,IAAI,CAAC;QAEhC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;QACxB;IACF;IAEA,IAAI,KAAa,EAAE,QAAmB,EAAE;QACtC,IAAI,UAAU;gBAQZ;YAPA,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YACrC,IAAI,WAAW;gBACb,MAAM,QAAQ,UAAU,OAAO,CAAC;gBAChC,IAAI,QAAQ,CAAC,GAAG;oBACd,UAAU,MAAM,CAAC,OAAO;gBAC1B;YACF;aACA,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,GAAG,CAAC,OAAO;QAC1B,OAAO;gBAEL;YADA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;aACtB,gBAAA,IAAI,CAAC,MAAM,cAAX,oCAAA,cAAa,GAAG,CAAC;QACnB;IACF;IAEA,KAAK,KAAa,EAAE,IAAU,EAAE;YAC9B;SAAA,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,IAAI,CAAC,OAAO;IAC3B;IAEA,cAAc,WAAmB,EAAE;QACjC,IAAI,CAAC,IAAI,CAAC,kBAAkB;IAC9B;IAEA,eAAe,WAAmB,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,mBAAmB;IAC/B;;QAxEA,+KAAQ,UAAwB;QAChC,+KAAQ,aAAqC,IAAI;;AAwEnD;AAEO,MAAM,gBAAgB,IAAI;uCAClB", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/components/providers.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useAuthStore } from '@/store/auth';\nimport { socketManager } from '@/lib/socket';\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  const { isAuthenticated, isHydrated } = useAuthStore();\n\n  useEffect(() => {\n    // Only run socket logic after hydration is complete\n    if (!isHydrated) return;\n\n    if (isAuthenticated) {\n      socketManager.connect();\n    } else {\n      socketManager.disconnect();\n    }\n\n    return () => {\n      socketManager.disconnect();\n    };\n  }, [isAuthenticated, isHydrated]);\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS,UAAU,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IACxB,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,oDAAoD;YACpD,IAAI,CAAC,YAAY;YAEjB,IAAI,iBAAiB;gBACnB,uHAAA,CAAA,gBAAa,CAAC,OAAO;YACvB,OAAO;gBACL,uHAAA,CAAA,gBAAa,CAAC,UAAU;YAC1B;YAEA;uCAAO;oBACL,uHAAA,CAAA,gBAAa,CAAC,UAAU;gBAC1B;;QACF;8BAAG;QAAC;QAAiB;KAAW;IAEhC,qBAAO;kBAAG;;AACZ;GAnBgB;;QAC0B,uHAAA,CAAA,eAAY;;;KADtC", "debugId": null}}]}