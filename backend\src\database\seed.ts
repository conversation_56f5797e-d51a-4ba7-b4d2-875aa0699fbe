import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create default admin user
  const hashedPassword = await bcrypt.hash('password123', 10);
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: 'ADMIN',
      isActive: true,
    },
  });

  console.log('✅ Created admin user:', adminUser.email);

  // Create default warehouse
  const warehouse = await prisma.warehouse.upsert({
    where: { id: 'default-warehouse' },
    update: {},
    create: {
      id: 'default-warehouse',
      name: 'Main Warehouse',
      address: '123 Warehouse Street, City, State 12345',
      isActive: true,
    },
  });

  console.log('✅ Created warehouse:', warehouse.name);

  // Create default category
  const category = await prisma.category.upsert({
    where: { name: 'General' },
    update: {},
    create: {
      name: 'General',
      description: 'General category for products',
    },
  });

  console.log('✅ Created category:', category.name);

  // Create default supplier
  const supplier = await prisma.supplier.upsert({
    where: { id: 'default-supplier' },
    update: {},
    create: {
      id: 'default-supplier',
      name: 'Default Supplier',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '456 Supplier Ave, City, State 12345',
      contactName: 'John Supplier',
      isActive: true,
    },
  });

  console.log('✅ Created supplier:', supplier.name);

  // Create sample product
  const product = await prisma.product.upsert({
    where: { sku: 'SAMPLE-001' },
    update: {},
    create: {
      sku: 'SAMPLE-001',
      name: 'Sample Product',
      description: 'This is a sample product for testing',
      barcode: '1234567890123',
      price: 29.99,
      cost: 15.00,
      weight: 1.5,
      dimensions: '{"length": 10, "width": 8, "height": 6}',
      isActive: true,
      categoryId: category.id,
      supplierId: supplier.id,
    },
  });

  console.log('✅ Created product:', product.name);

  // Create inventory record
  const inventory = await prisma.inventory.upsert({
    where: {
      productId_warehouseId: {
        productId: product.id,
        warehouseId: warehouse.id,
      },
    },
    update: {},
    create: {
      productId: product.id,
      warehouseId: warehouse.id,
      quantity: 100,
      reservedQty: 0,
      minThreshold: 10,
      maxThreshold: 500,
      location: 'A1-B2-C3',
    },
  });

  console.log('✅ Created inventory record for:', product.name);

  // Create default settings
  const settings = [
    { key: 'company_name', value: 'Warehouse Management System', category: 'general' },
    { key: 'currency', value: 'USD', category: 'general' },
    { key: 'timezone', value: 'America/New_York', category: 'general' },
    { key: 'low_stock_threshold', value: '10', category: 'inventory' },
    { key: 'auto_reorder', value: 'false', category: 'inventory' },
  ];

  for (const setting of settings) {
    await prisma.setting.upsert({
      where: { key: setting.key },
      update: { value: setting.value },
      create: setting,
    });
  }

  console.log('✅ Created default settings');

  console.log('🎉 Database seeding completed successfully!');
  console.log('');
  console.log('🔐 Default login credentials:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: password123');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
