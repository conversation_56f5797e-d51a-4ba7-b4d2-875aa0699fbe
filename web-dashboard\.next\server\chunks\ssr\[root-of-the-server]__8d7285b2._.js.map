{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/components/ui/Breadcrumb.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { ChevronRight, Home } from 'lucide-react';\n\ninterface BreadcrumbItem {\n  label: string;\n  href: string;\n}\n\ninterface BreadcrumbProps {\n  items?: BreadcrumbItem[];\n  className?: string;\n}\n\nexport default function Breadcrumb({ items, className = '' }: BreadcrumbProps) {\n  const pathname = usePathname();\n\n  // Auto-generate breadcrumbs from pathname if items not provided\n  const breadcrumbItems = items || generateBreadcrumbs(pathname);\n\n  return (\n    <nav className={`flex ${className}`} aria-label=\"Breadcrumb\">\n      <ol className=\"inline-flex items-center space-x-1 md:space-x-3\">\n        <li className=\"inline-flex items-center\">\n          <Link\n            href=\"/dashboard\"\n            className=\"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors\"\n          >\n            <Home className=\"h-4 w-4 mr-2\" />\n            Dashboard\n          </Link>\n        </li>\n        {breadcrumbItems.map((item, index) => (\n          <li key={item.href}>\n            <div className=\"flex items-center\">\n              <ChevronRight className=\"h-4 w-4 text-gray-400 mx-1\" />\n              {index === breadcrumbItems.length - 1 ? (\n                <span className=\"text-sm font-medium text-gray-500\" aria-current=\"page\">\n                  {item.label}\n                </span>\n              ) : (\n                <Link\n                  href={item.href}\n                  className=\"text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors\"\n                >\n                  {item.label}\n                </Link>\n              )}\n            </div>\n          </li>\n        ))}\n      </ol>\n    </nav>\n  );\n}\n\nfunction generateBreadcrumbs(pathname: string): BreadcrumbItem[] {\n  const segments = pathname.split('/').filter(Boolean);\n  const breadcrumbs: BreadcrumbItem[] = [];\n\n  // Skip the first segment if it's 'dashboard'\n  const startIndex = segments[0] === 'dashboard' ? 1 : 0;\n\n  for (let i = startIndex; i < segments.length; i++) {\n    const segment = segments[i];\n    const href = '/' + segments.slice(0, i + 1).join('/');\n    \n    // Convert segment to readable label\n    const label = segment\n      .split('-')\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n      .join(' ');\n\n    breadcrumbs.push({ label, href });\n  }\n\n  return breadcrumbs;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAgBe,SAAS,WAAW,EAAE,KAAK,EAAE,YAAY,EAAE,EAAmB;IAC3E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,kBAAkB,SAAS,oBAAoB;IAErD,qBACE,8OAAC;QAAI,WAAW,CAAC,KAAK,EAAE,WAAW;QAAE,cAAW;kBAC9C,cAAA,8OAAC;YAAG,WAAU;;8BACZ,8OAAC;oBAAG,WAAU;8BACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,mMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;gBAIpC,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC;kCACC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCACvB,UAAU,gBAAgB,MAAM,GAAG,kBAClC,8OAAC;oCAAK,WAAU;oCAAoC,gBAAa;8CAC9D,KAAK,KAAK;;;;;yDAGb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,KAAK;;;;;;;;;;;;uBAZV,KAAK,IAAI;;;;;;;;;;;;;;;;AAqB5B;AAEA,SAAS,oBAAoB,QAAgB;IAC3C,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAC5C,MAAM,cAAgC,EAAE;IAExC,6CAA6C;IAC7C,MAAM,aAAa,QAAQ,CAAC,EAAE,KAAK,cAAc,IAAI;IAErD,IAAK,IAAI,IAAI,YAAY,IAAI,SAAS,MAAM,EAAE,IAAK;QACjD,MAAM,UAAU,QAAQ,CAAC,EAAE;QAC3B,MAAM,OAAO,MAAM,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;QAEjD,oCAAoC;QACpC,MAAM,QAAQ,QACX,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;QAER,YAAY,IAAI,CAAC;YAAE;YAAO;QAAK;IACjC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/components/ui/BackButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { ArrowLeft } from 'lucide-react';\n\ninterface BackButtonProps {\n  fallbackUrl?: string;\n  className?: string;\n  children?: React.ReactNode;\n}\n\nexport default function BackButton({ \n  fallbackUrl = '/dashboard', \n  className = '',\n  children \n}: BackButtonProps) {\n  const router = useRouter();\n\n  const handleGoBack = () => {\n    // Check if there's history to go back to (only on client-side)\n    if (typeof window !== 'undefined' && window.history.length > 1) {\n      router.back();\n    } else {\n      // Fallback to a specific URL if no history\n      router.push(fallbackUrl);\n    }\n  };\n\n  return (\n    <button\n      onClick={handleGoBack}\n      className={`inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors ${className}`}\n    >\n      <ArrowLeft className=\"h-4 w-4 mr-2\" />\n      {children || 'Back'}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWe,SAAS,WAAW,EACjC,cAAc,YAAY,EAC1B,YAAY,EAAE,EACd,QAAQ,EACQ;IAChB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,+DAA+D;QAC/D,IAAI,gBAAkB,eAAe,OAAO,OAAO,CAAC,MAAM,GAAG;;aAEtD;YACL,2CAA2C;YAC3C,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAW,CAAC,2NAA2N,EAAE,WAAW;;0BAEpP,8OAAC,gNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YACpB,YAAY;;;;;;;AAGnB", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/components/ui/PageHeader.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport Breadcrumb from './Breadcrumb';\nimport BackButton from './BackButton';\n\ninterface PageHeaderProps {\n  title: string;\n  description?: string;\n  showBreadcrumb?: boolean;\n  showBackButton?: boolean;\n  backButtonFallback?: string;\n  actions?: ReactNode;\n  className?: string;\n}\n\nexport default function PageHeader({\n  title,\n  description,\n  showBreadcrumb = true,\n  showBackButton = false,\n  backButtonFallback = '/dashboard',\n  actions,\n  className = ''\n}: PageHeaderProps) {\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {showBreadcrumb && <Breadcrumb />}\n      \n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div className=\"flex items-start gap-4\">\n          {showBackButton && (\n            <div className=\"flex-shrink-0 pt-1\">\n              <BackButton fallbackUrl={backButtonFallback} />\n            </div>\n          )}\n          <div>\n            <h1 className=\"text-2xl sm:text-3xl font-bold text-gray-900\">{title}</h1>\n            {description && (\n              <p className=\"mt-1 text-sm sm:text-base text-gray-600\">{description}</p>\n            )}\n          </div>\n        </div>\n        \n        {actions && (\n          <div className=\"flex-shrink-0\">\n            {actions}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAgBe,SAAS,WAAW,EACjC,KAAK,EACL,WAAW,EACX,iBAAiB,IAAI,EACrB,iBAAiB,KAAK,EACtB,qBAAqB,YAAY,EACjC,OAAO,EACP,YAAY,EAAE,EACE;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;YACrC,gCAAkB,8OAAC,sIAAA,CAAA,UAAU;;;;;0BAE9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,gCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sIAAA,CAAA,UAAU;oCAAC,aAAa;;;;;;;;;;;0CAG7B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;oCAC7D,6BACC,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;oBAK7D,yBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: `${API_BASE_URL}/api`,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests\napi.interceptors.request.use((config) => {\n  if (typeof window !== 'undefined') {\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n  }\n  return config;\n});\n\n// Handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401 && typeof window !== 'undefined') {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authApi = {\n  login: (email: string, password: string) =>\n    api.post('/auth/login', { email, password }),\n  register: (userData: any) =>\n    api.post('/auth/register', userData),\n  verify: () =>\n    api.get('/auth/verify'),\n};\n\n// Products API\nexport const productsApi = {\n  getAll: (params?: any) =>\n    api.get('/products', { params }),\n  getById: (id: string) =>\n    api.get(`/products/${id}`),\n  create: (data: any) =>\n    api.post('/products', data),\n  update: (id: string, data: any) =>\n    api.put(`/products/${id}`, data),\n  delete: (id: string) =>\n    api.delete(`/products/${id}`),\n  getByBarcode: (barcode: string) =>\n    api.get(`/products/barcode/${barcode}`),\n};\n\n// Inventory API\nexport const inventoryApi = {\n  getAll: (params?: any) =>\n    api.get('/inventory', { params }),\n  getByProduct: (productId: string, warehouseId: string) =>\n    api.get(`/inventory/${productId}/${warehouseId}`),\n  update: (productId: string, warehouseId: string, data: any) =>\n    api.put(`/inventory/${productId}/${warehouseId}/update`, data),\n  adjust: (productId: string, warehouseId: string, data: any) =>\n    api.post(`/inventory/${productId}/${warehouseId}/adjust`, data),\n  getLowStock: () =>\n    api.get('/inventory/low-stock'),\n};\n\n// Orders API\nexport const ordersApi = {\n  getAll: (params?: any) =>\n    api.get('/orders', { params }),\n  getById: (id: string) =>\n    api.get(`/orders/${id}`),\n  create: (data: any) =>\n    api.post('/orders', data),\n  update: (id: string, data: any) =>\n    api.put(`/orders/${id}`, data),\n  delete: (id: string) =>\n    api.delete(`/orders/${id}`),\n  updateStatus: (id: string, status: string, notes?: string) =>\n    api.put(`/orders/${id}/status`, { status, notes }),\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  getStats: () =>\n    api.get('/dashboard/stats'),\n};\n\n// Categories API\nexport const categoriesApi = {\n  getAll: () =>\n    api.get('/categories'),\n  getById: (id: string) =>\n    api.get(`/categories/${id}`),\n  create: (data: any) =>\n    api.post('/categories', data),\n  update: (id: string, data: any) =>\n    api.put(`/categories/${id}`, data),\n  delete: (id: string) =>\n    api.delete(`/categories/${id}`),\n};\n\n// Suppliers API\nexport const suppliersApi = {\n  getAll: () =>\n    api.get('/suppliers'),\n  getById: (id: string) =>\n    api.get(`/suppliers/${id}`),\n  create: (data: any) =>\n    api.post('/suppliers', data),\n  update: (id: string, data: any) =>\n    api.put(`/suppliers/${id}`, data),\n  delete: (id: string) =>\n    api.delete(`/suppliers/${id}`),\n};\n\n// Warehouses API\nexport const warehousesApi = {\n  getAll: () =>\n    api.get('/warehouses'),\n  getById: (id: string) =>\n    api.get(`/warehouses/${id}`),\n  create: (data: any) =>\n    api.post('/warehouses', data),\n  update: (id: string, data: any) =>\n    api.put(`/warehouses/${id}`, data),\n  delete: (id: string) =>\n    api.delete(`/warehouses/${id}`),\n};\n\n// Users API\nexport const usersApi = {\n  getAll: (params?: any) =>\n    api.get('/users', { params }),\n  getById: (id: string) =>\n    api.get(`/users/${id}`),\n  create: (data: any) =>\n    api.post('/users', data),\n  update: (id: string, data: any) =>\n    api.put(`/users/${id}`, data),\n  delete: (id: string) =>\n    api.delete(`/users/${id}`),\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAEA,MAAM,eAAe,6DAAmC;AAExD,wBAAwB;AACxB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS,GAAG,aAAa,IAAI,CAAC;IAC9B,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,6BAA6B;AAC7B,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC5B;;IAMA,OAAO;AACT;AAEA,qBAAqB;AACrB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,gBAAkB;;IAKxD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,OAAO,CAAC,OAAe,WACrB,IAAI,IAAI,CAAC,eAAe;YAAE;YAAO;QAAS;IAC5C,UAAU,CAAC,WACT,IAAI,IAAI,CAAC,kBAAkB;IAC7B,QAAQ,IACN,IAAI,GAAG,CAAC;AACZ;AAGO,MAAM,cAAc;IACzB,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,aAAa;YAAE;QAAO;IAChC,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;IAC3B,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,aAAa;IACxB,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;IAC7B,QAAQ,CAAC,KACP,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;IAC9B,cAAc,CAAC,UACb,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,SAAS;AAC1C;AAGO,MAAM,eAAe;IAC1B,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,cAAc;YAAE;QAAO;IACjC,cAAc,CAAC,WAAmB,cAChC,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,aAAa;IAClD,QAAQ,CAAC,WAAmB,aAAqB,OAC/C,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,YAAY,OAAO,CAAC,EAAE;IAC3D,QAAQ,CAAC,WAAmB,aAAqB,OAC/C,IAAI,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,YAAY,OAAO,CAAC,EAAE;IAC5D,aAAa,IACX,IAAI,GAAG,CAAC;AACZ;AAGO,MAAM,YAAY;IACvB,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,WAAW;YAAE;QAAO;IAC9B,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI;IACzB,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,WAAW;IACtB,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE;IAC3B,QAAQ,CAAC,KACP,IAAI,MAAM,CAAC,CAAC,QAAQ,EAAE,IAAI;IAC5B,cAAc,CAAC,IAAY,QAAgB,QACzC,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,EAAE;YAAE;YAAQ;QAAM;AACpD;AAGO,MAAM,eAAe;IAC1B,UAAU,IACR,IAAI,GAAG,CAAC;AACZ;AAGO,MAAM,gBAAgB;IAC3B,QAAQ,IACN,IAAI,GAAG,CAAC;IACV,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;IAC7B,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,eAAe;IAC1B,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;IAC/B,QAAQ,CAAC,KACP,IAAI,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;AAClC;AAGO,MAAM,eAAe;IAC1B,QAAQ,IACN,IAAI,GAAG,CAAC;IACV,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI;IAC5B,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,cAAc;IACzB,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE;IAC9B,QAAQ,CAAC,KACP,IAAI,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;AACjC;AAGO,MAAM,gBAAgB;IAC3B,QAAQ,IACN,IAAI,GAAG,CAAC;IACV,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;IAC7B,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,eAAe;IAC1B,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;IAC/B,QAAQ,CAAC,KACP,IAAI,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;AAClC;AAGO,MAAM,WAAW;IACtB,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,UAAU;YAAE;QAAO;IAC7B,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI;IACxB,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,UAAU;IACrB,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;IAC1B,QAAQ,CAAC,KACP,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;AAC7B;uCAEe", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/app/dashboard/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Plus, Search, Filter, Eye, Edit, Trash2, Package } from 'lucide-react';\nimport PageHeader from '@/components/ui/PageHeader';\nimport { productsApi } from '@/lib/api';\n\ninterface Product {\n  id: string;\n  sku: string;\n  name: string;\n  description: string;\n  price: number;\n  cost: number;\n  category: {\n    id: string;\n    name: string;\n  };\n  supplier: {\n    id: string;\n    name: string;\n  };\n  isActive: boolean;\n  createdAt: string;\n}\n\nexport default function ProductsPage() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [error, setError] = useState('');\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await productsApi.getAll({ search: searchTerm });\n      setProducts(response.data.products || []);\n    } catch (err: any) {\n      setError(err.response?.data?.error || 'Failed to fetch products');\n      console.error('Error fetching products:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      if (searchTerm !== '') {\n        fetchProducts();\n      }\n    }, 500);\n\n    return () => clearTimeout(timeoutId);\n  }, [searchTerm]);\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <PageHeader\n          title=\"Products\"\n          description=\"Manage your product catalog\"\n        />\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"space-y-6\">\n        <PageHeader\n          title=\"Products\"\n          description=\"Manage your product catalog\"\n        />\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex\">\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-red-800\">Error loading products</h3>\n              <div className=\"mt-2 text-sm text-red-700\">\n                <p>{error}</p>\n              </div>\n              <div className=\"mt-4\">\n                <button\n                  type=\"button\"\n                  onClick={fetchProducts}\n                  className=\"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200\"\n                >\n                  Try again\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Products</h1>\n          <p className=\"text-gray-600\">Manage your product catalog</p>\n        </div>\n        <button\n          type=\"button\"\n          className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\"\n          onClick={() => console.log('Add product clicked')}\n        >\n          <Plus className=\"h-4 w-4\" />\n          <span>Add Product</span>\n        </button>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1 relative\">\n              <label htmlFor=\"product-search\" className=\"sr-only\">Search products</label>\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <input\n                id=\"product-search\"\n                type=\"text\"\n                placeholder=\"Search products...\"\n                title=\"Search products by name or SKU\"\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n            <button\n              type=\"button\"\n              className=\"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2\"\n              onClick={() => console.log('Filter clicked')}\n            >\n              <Filter className=\"h-4 w-4\" />\n              <span>Filter</span>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Product\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  SKU\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Category\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Price\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Cost\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Status\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {products.map((product) => (\n                <tr key={product.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0 h-10 w-10\">\n                        <div className=\"h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center\">\n                          <Package className=\"h-5 w-5 text-gray-500\" />\n                        </div>\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">{product.name}</div>\n                        <div className=\"text-sm text-gray-500\">{product.description}</div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.sku}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.category?.name || 'N/A'}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    ${product.price.toFixed(2)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    ${product.cost.toFixed(2)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                      product.isActive \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {product.isActive ? 'Active' : 'Inactive'}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <button\n                        type=\"button\"\n                        title=\"View product\"\n                        className=\"text-blue-600 hover:text-blue-900\"\n                        onClick={() => console.log('View product:', product.id)}\n                      >\n                        <Eye className=\"h-4 w-4\" />\n                      </button>\n                      <button\n                        type=\"button\"\n                        title=\"Edit product\"\n                        className=\"text-green-600 hover:text-green-900\"\n                        onClick={() => console.log('Edit product:', product.id)}\n                      >\n                        <Edit className=\"h-4 w-4\" />\n                      </button>\n                      <button\n                        type=\"button\"\n                        title=\"Delete product\"\n                        className=\"text-red-600 hover:text-red-900\"\n                        onClick={() => console.log('Delete product:', product.id)}\n                      >\n                        <Trash2 className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AA0Be,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,iHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBAAE,QAAQ;YAAW;YAC/D,YAAY,SAAS,IAAI,CAAC,QAAQ,IAAI,EAAE;QAC1C,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;YACtC,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,eAAe,IAAI;gBACrB;YACF;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAW;IAEf,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,UAAU;oBACT,OAAM;oBACN,aAAY;;;;;;8BAEd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,UAAU;oBACT,OAAM;oBACN,aAAY;;;;;;8BAEd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDAAG;;;;;;;;;;;8CAEN,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASf;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,QAAQ,GAAG,CAAC;;0CAE3B,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAIV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAiB,WAAU;sDAAU;;;;;;sDACpD,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,aAAY;4CACZ,OAAM;4CACN,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAGjD,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,QAAQ,GAAG,CAAC;;sDAE3B,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;;;;;;;;;;;;8CAKnG,8OAAC;oCAAM,WAAU;8CACd,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;;;;;;;;;;;0EAGvB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAqC,QAAQ,IAAI;;;;;;kFAChE,8OAAC;wEAAI,WAAU;kFAAyB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;8DAIjE,8OAAC;oDAAG,WAAU;8DACX,QAAQ,GAAG;;;;;;8DAEd,8OAAC;oDAAG,WAAU;8DACX,QAAQ,QAAQ,EAAE,QAAQ;;;;;;8DAE7B,8OAAC;oDAAG,WAAU;;wDAAoD;wDAC9D,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;8DAE1B,8OAAC;oDAAG,WAAU;;wDAAoD;wDAC9D,QAAQ,IAAI,CAAC,OAAO,CAAC;;;;;;;8DAEzB,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAW,CAAC,yDAAyD,EACzE,QAAQ,QAAQ,GACZ,gCACA,2BACJ;kEACC,QAAQ,QAAQ,GAAG,WAAW;;;;;;;;;;;8DAGnC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,OAAM;gEACN,WAAU;gEACV,SAAS,IAAM,QAAQ,GAAG,CAAC,iBAAiB,QAAQ,EAAE;0EAEtD,cAAA,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEACC,MAAK;gEACL,OAAM;gEACN,WAAU;gEACV,SAAS,IAAM,QAAQ,GAAG,CAAC,iBAAiB,QAAQ,EAAE;0EAEtD,cAAA,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC;gEACC,MAAK;gEACL,OAAM;gEACN,WAAU;gEACV,SAAS,IAAM,QAAQ,GAAG,CAAC,mBAAmB,QAAQ,EAAE;0EAExD,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CA3DjB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEnC", "debugId": null}}]}