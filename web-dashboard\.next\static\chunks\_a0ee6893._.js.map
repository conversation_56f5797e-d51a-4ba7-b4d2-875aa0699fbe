{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/auth';\n\nexport default function Home() {\n  const router = useRouter();\n  const { isAuthenticated } = useAuthStore();\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/dashboard');\n    } else {\n      router.push('/login');\n    }\n  }, [isAuthenticated, router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"></div>\n        <p className=\"mt-4 text-gray-600\">Loading...</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAiB;KAAO;IAE5B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;;;;;;AAI1C;GApBwB;;QACP,qIAAA,CAAA,YAAS;QACI,uHAAA,CAAA,eAAY;;;KAFlB", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}