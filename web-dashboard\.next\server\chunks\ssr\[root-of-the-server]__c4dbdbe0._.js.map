{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function formatNumber(num: number): string {\n  return new Intl.NumberFormat('en-US').format(num)\n}\n\nexport function getErrorMessage(error: any): string {\n  if (error.response?.data?.error) {\n    return error.response.data.error;\n  }\n  if (error.message) {\n    return error.message;\n  }\n  return 'An unexpected error occurred';\n}\n\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    // Order statuses\n    PENDING: 'bg-yellow-100 text-yellow-800',\n    PROCESSING: 'bg-blue-100 text-blue-800',\n    PICKING: 'bg-purple-100 text-purple-800',\n    PACKED: 'bg-indigo-100 text-indigo-800',\n    SHIPPED: 'bg-green-100 text-green-800',\n    DELIVERED: 'bg-green-100 text-green-800',\n    CANCELLED: 'bg-red-100 text-red-800',\n\n    // Priority levels\n    LOW: 'bg-gray-100 text-gray-800',\n    NORMAL: 'bg-blue-100 text-blue-800',\n    HIGH: 'bg-orange-100 text-orange-800',\n    URGENT: 'bg-red-100 text-red-800',\n\n    // User roles\n    ADMIN: 'bg-red-100 text-red-800',\n    MANAGER: 'bg-blue-100 text-blue-800',\n    EMPLOYEE: 'bg-green-100 text-green-800',\n\n    // General statuses\n    ACTIVE: 'bg-green-100 text-green-800',\n    INACTIVE: 'bg-red-100 text-red-800',\n  };\n\n  return statusColors[status.toUpperCase()] || 'bg-gray-100 text-gray-800';\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,gBAAgB,KAAU;IACxC,IAAI,MAAM,QAAQ,EAAE,MAAM,OAAO;QAC/B,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK;IAClC;IACA,IAAI,MAAM,OAAO,EAAE;QACjB,OAAO,MAAM,OAAO;IACtB;IACA,OAAO;AACT;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,iBAAiB;QACjB,SAAS;QACT,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,SAAS;QACT,WAAW;QACX,WAAW;QAEX,kBAAkB;QAClB,KAAK;QACL,QAAQ;QACR,MAAM;QACN,QAAQ;QAER,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;QAEV,mBAAmB;QACnB,QAAQ;QACR,UAAU;IACZ;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/app/dashboard/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuthStore } from '@/store/auth';\nimport { Button } from '@/components/ui/button';\nimport { \n  LayoutDashboard, \n  Package, \n  ShoppingCart, \n  Warehouse, \n  Users, \n  Settings,\n  Menu,\n  X,\n  LogOut,\n  Bell\n} from 'lucide-react';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },\n  { name: 'Inventory', href: '/dashboard/inventory', icon: Package },\n  { name: 'Orders', href: '/dashboard/orders', icon: ShoppingCart },\n  { name: 'Products', href: '/dashboard/products', icon: Package },\n  { name: 'Warehouses', href: '/dashboard/warehouses', icon: Warehouse },\n  { name: 'Users', href: '/dashboard/users', icon: Users },\n  { name: 'Settings', href: '/dashboard/settings', icon: Settings },\n];\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, isAuthenticated, logout } = useAuthStore();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  useEffect(() => {\n    if (!isAuthenticated) {\n      router.push('/login');\n    }\n  }, [isAuthenticated, router]);\n\n  const handleLogout = () => {\n    logout();\n    router.push('/login');\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\">\n          <div className=\"flex h-16 items-center justify-between px-4 border-b\">\n            <h1 className=\"text-xl font-bold text-gray-900\">Warehouse</h1>\n            <button\n              type=\"button\"\n              onClick={() => setSidebarOpen(false)}\n              aria-label=\"Close sidebar\"\n            >\n              <X className=\"h-6 w-6\" />\n            </button>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-2\">\n            {navigation.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                    isActive\n                      ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-700 hover:bg-gray-100'\n                  }`}\n                  onClick={() => setSidebarOpen(false)}\n                >\n                  <Icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-4 border-b\">\n            <Warehouse className=\"h-8 w-8 text-blue-600 mr-2\" />\n            <h1 className=\"text-xl font-bold text-gray-900\">Warehouse</h1>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-2\">\n            {navigation.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                    isActive\n                      ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-700 hover:bg-gray-100'\n                  }`}\n                >\n                  <Icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </nav>\n          <div className=\"p-4 border-t\">\n            <div className=\"flex items-center mb-3\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium\">\n                {user?.firstName?.[0]}{user?.lastName?.[0]}\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-900\">\n                  {user?.firstName} {user?.lastName}\n                </p>\n                <p className=\"text-xs text-gray-500\">{user?.role}</p>\n              </div>\n            </div>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleLogout}\n              className=\"w-full\"\n            >\n              <LogOut className=\"mr-2 h-4 w-4\" />\n              Sign Out\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200\">\n          <div className=\"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\">\n            <button\n              type=\"button\"\n              className=\"lg:hidden\"\n              onClick={() => setSidebarOpen(true)}\n              aria-label=\"Open sidebar menu\"\n            >\n              <Menu className=\"h-6 w-6\" />\n            </button>\n            \n            <div className=\"flex items-center space-x-4\">\n              <button\n                type=\"button\"\n                className=\"p-2 text-gray-400 hover:text-gray-600\"\n                aria-label=\"View notifications\"\n              >\n                <Bell className=\"h-5 w-5\" />\n              </button>\n              <div className=\"lg:hidden flex items-center\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium\">\n                  {user?.firstName?.[0]}{user?.lastName?.[0]}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"p-4 sm:p-6 lg:p-8\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAoBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,4NAAA,CAAA,kBAAe;IAAC;IAC/D;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,wMAAA,CAAA,UAAO;IAAC;IACjE;QAAE,MAAM;QAAU,MAAM;QAAqB,MAAM,sNAAA,CAAA,eAAY;IAAC;IAChE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,wMAAA,CAAA,UAAO;IAAC;IAC/D;QAAE,MAAM;QAAc,MAAM;QAAyB,MAAM,4MAAA,CAAA,YAAS;IAAC;IACrE;QAAE,MAAM;QAAS,MAAM;QAAoB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACvD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACjE;AAEc,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB;YACpB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,UAAU,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,cAAW;kDAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,yDACA,mCACJ;wCACF,SAAS,IAAM,eAAe;;0DAE9B,8OAAC;gDAAK,WAAU;;;;;;4CACf,KAAK,IAAI;;uCAVL,KAAK,IAAI;;;;;gCAapB;;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;sCAElD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,yDACA,mCACJ;;sDAEF,8OAAC;4CAAK,WAAU;;;;;;wCACf,KAAK,IAAI;;mCATL,KAAK,IAAI;;;;;4BAYpB;;;;;;sCAEF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,MAAM,WAAW,CAAC,EAAE;gDAAE,MAAM,UAAU,CAAC,EAAE;;;;;;;sDAE5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;;wDACV,MAAM;wDAAU;wDAAE,MAAM;;;;;;;8DAE3B,8OAAC;oDAAE,WAAU;8DAAyB,MAAM;;;;;;;;;;;;;;;;;;8CAGhD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;oCAC9B,cAAW;8CAEX,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,MAAM,WAAW,CAAC,EAAE;oDAAE,MAAM,UAAU,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpD,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}