import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

// Create axios instance
const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authApi = {
  login: (email: string, password: string) =>
    api.post('/auth/login', { email, password }),
  register: (userData: any) =>
    api.post('/auth/register', userData),
  verify: () =>
    api.get('/auth/verify'),
};

// Products API
export const productsApi = {
  getAll: (params?: any) =>
    api.get('/products', { params }),
  getById: (id: string) =>
    api.get(`/products/${id}`),
  create: (data: any) =>
    api.post('/products', data),
  update: (id: string, data: any) =>
    api.put(`/products/${id}`, data),
  delete: (id: string) =>
    api.delete(`/products/${id}`),
  getByBarcode: (barcode: string) =>
    api.get(`/products/barcode/${barcode}`),
};

// Inventory API
export const inventoryApi = {
  getAll: (params?: any) =>
    api.get('/inventory', { params }),
  getByProduct: (productId: string, warehouseId: string) =>
    api.get(`/inventory/${productId}/${warehouseId}`),
  update: (productId: string, warehouseId: string, data: any) =>
    api.put(`/inventory/${productId}/${warehouseId}/update`, data),
  adjust: (productId: string, warehouseId: string, data: any) =>
    api.post(`/inventory/${productId}/${warehouseId}/adjust`, data),
  getLowStock: () =>
    api.get('/inventory/low-stock'),
};

// Orders API
export const ordersApi = {
  getAll: (params?: any) =>
    api.get('/orders', { params }),
  getById: (id: string) =>
    api.get(`/orders/${id}`),
  create: (data: any) =>
    api.post('/orders', data),
  update: (id: string, data: any) =>
    api.put(`/orders/${id}`, data),
  delete: (id: string) =>
    api.delete(`/orders/${id}`),
  updateStatus: (id: string, status: string, notes?: string) =>
    api.put(`/orders/${id}/status`, { status, notes }),
};

// Dashboard API
export const dashboardApi = {
  getStats: () =>
    api.get('/dashboard/stats'),
};

// Categories API
export const categoriesApi = {
  getAll: () =>
    api.get('/categories'),
  getById: (id: string) =>
    api.get(`/categories/${id}`),
  create: (data: any) =>
    api.post('/categories', data),
  update: (id: string, data: any) =>
    api.put(`/categories/${id}`, data),
  delete: (id: string) =>
    api.delete(`/categories/${id}`),
};

// Suppliers API
export const suppliersApi = {
  getAll: () =>
    api.get('/suppliers'),
  getById: (id: string) =>
    api.get(`/suppliers/${id}`),
  create: (data: any) =>
    api.post('/suppliers', data),
  update: (id: string, data: any) =>
    api.put(`/suppliers/${id}`, data),
  delete: (id: string) =>
    api.delete(`/suppliers/${id}`),
};

// Warehouses API
export const warehousesApi = {
  getAll: () =>
    api.get('/warehouses'),
  getById: (id: string) =>
    api.get(`/warehouses/${id}`),
  create: (data: any) =>
    api.post('/warehouses', data),
  update: (id: string, data: any) =>
    api.put(`/warehouses/${id}`, data),
  delete: (id: string) =>
    api.delete(`/warehouses/${id}`),
};

// Users API
export const usersApi = {
  getAll: (params?: any) =>
    api.get('/users', { params }),
  getById: (id: string) =>
    api.get(`/users/${id}`),
  create: (data: any) =>
    api.post('/users', data),
  update: (id: string, data: any) =>
    api.put(`/users/${id}`, data),
  delete: (id: string) =>
    api.delete(`/users/${id}`),
};

export default api;
