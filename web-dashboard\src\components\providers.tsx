'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/store/auth';
import { socketManager } from '@/lib/socket';

export function Providers({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isHydrated } = useAuthStore();

  useEffect(() => {
    // Only run socket logic after hydration is complete
    if (!isHydrated) return;

    if (isAuthenticated) {
      socketManager.connect();
    } else {
      socketManager.disconnect();
    }

    return () => {
      socketManager.disconnect();
    };
  }, [isAuthenticated, isHydrated]);

  return <>{children}</>;
}
