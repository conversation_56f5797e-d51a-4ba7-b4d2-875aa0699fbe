{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: `${API_BASE_URL}/api`,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests\napi.interceptors.request.use((config) => {\n  if (typeof window !== 'undefined') {\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n  }\n  return config;\n});\n\n// Handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401 && typeof window !== 'undefined') {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authApi = {\n  login: (email: string, password: string) =>\n    api.post('/auth/login', { email, password }),\n  register: (userData: any) =>\n    api.post('/auth/register', userData),\n  verify: () =>\n    api.get('/auth/verify'),\n};\n\n// Products API\nexport const productsApi = {\n  getAll: (params?: any) =>\n    api.get('/products', { params }),\n  getById: (id: string) =>\n    api.get(`/products/${id}`),\n  create: (data: any) =>\n    api.post('/products', data),\n  update: (id: string, data: any) =>\n    api.put(`/products/${id}`, data),\n  getByBarcode: (barcode: string) =>\n    api.get(`/products/barcode/${barcode}`),\n};\n\n// Inventory API\nexport const inventoryApi = {\n  getAll: (params?: any) =>\n    api.get('/inventory', { params }),\n  getByProduct: (productId: string, warehouseId: string) =>\n    api.get(`/inventory/${productId}/${warehouseId}`),\n  update: (productId: string, warehouseId: string, data: any) =>\n    api.put(`/inventory/${productId}/${warehouseId}/update`, data),\n  adjust: (productId: string, warehouseId: string, data: any) =>\n    api.post(`/inventory/${productId}/${warehouseId}/adjust`, data),\n  getLowStock: () =>\n    api.get('/inventory/low-stock'),\n};\n\n// Orders API\nexport const ordersApi = {\n  getAll: (params?: any) =>\n    api.get('/orders', { params }),\n  getById: (id: string) =>\n    api.get(`/orders/${id}`),\n  create: (data: any) =>\n    api.post('/orders', data),\n  updateStatus: (id: string, status: string, notes?: string) =>\n    api.put(`/orders/${id}/status`, { status, notes }),\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  getStats: () =>\n    api.get('/dashboard/stats'),\n};\n\n// Categories API\nexport const categoriesApi = {\n  getAll: () =>\n    api.get('/categories'),\n  create: (data: any) =>\n    api.post('/categories', data),\n};\n\n// Suppliers API\nexport const suppliersApi = {\n  getAll: () =>\n    api.get('/suppliers'),\n  create: (data: any) =>\n    api.post('/suppliers', data),\n};\n\n// Warehouses API\nexport const warehousesApi = {\n  getAll: () =>\n    api.get('/warehouses'),\n  create: (data: any) =>\n    api.post('/warehouses', data),\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;AAEqB;AAFrB;;AAEA,MAAM,eAAe,6DAAmC;AAExD,wBAAwB;AACxB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS,AAAC,GAAe,OAAb,cAAa;IACzB,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,6BAA6B;AAC7B,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC5B,wCAAmC;QACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,OAAO,OAAO,CAAC,aAAa,GAAG,AAAC,UAAe,OAAN;QAC3C;IACF;IACA,OAAO;AACT;AAEA,qBAAqB;AACrB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;QACK;IAAJ,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,OAAO,aAAkB,aAAa;QACnE,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,OAAO,CAAC,OAAe,WACrB,IAAI,IAAI,CAAC,eAAe;YAAE;YAAO;QAAS;IAC5C,UAAU,CAAC,WACT,IAAI,IAAI,CAAC,kBAAkB;IAC7B,QAAQ,IACN,IAAI,GAAG,CAAC;AACZ;AAGO,MAAM,cAAc;IACzB,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,aAAa;YAAE;QAAO;IAChC,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,AAAC,aAAe,OAAH;IACvB,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,aAAa;IACxB,QAAQ,CAAC,IAAY,OACnB,IAAI,GAAG,CAAC,AAAC,aAAe,OAAH,KAAM;IAC7B,cAAc,CAAC,UACb,IAAI,GAAG,CAAC,AAAC,qBAA4B,OAAR;AACjC;AAGO,MAAM,eAAe;IAC1B,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,cAAc;YAAE;QAAO;IACjC,cAAc,CAAC,WAAmB,cAChC,IAAI,GAAG,CAAC,AAAC,cAA0B,OAAb,WAAU,KAAe,OAAZ;IACrC,QAAQ,CAAC,WAAmB,aAAqB,OAC/C,IAAI,GAAG,CAAC,AAAC,cAA0B,OAAb,WAAU,KAAe,OAAZ,aAAY,YAAU;IAC3D,QAAQ,CAAC,WAAmB,aAAqB,OAC/C,IAAI,IAAI,CAAC,AAAC,cAA0B,OAAb,WAAU,KAAe,OAAZ,aAAY,YAAU;IAC5D,aAAa,IACX,IAAI,GAAG,CAAC;AACZ;AAGO,MAAM,YAAY;IACvB,QAAQ,CAAC,SACP,IAAI,GAAG,CAAC,WAAW;YAAE;QAAO;IAC9B,SAAS,CAAC,KACR,IAAI,GAAG,CAAC,AAAC,WAAa,OAAH;IACrB,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,WAAW;IACtB,cAAc,CAAC,IAAY,QAAgB,QACzC,IAAI,GAAG,CAAC,AAAC,WAAa,OAAH,IAAG,YAAU;YAAE;YAAQ;QAAM;AACpD;AAGO,MAAM,eAAe;IAC1B,UAAU,IACR,IAAI,GAAG,CAAC;AACZ;AAGO,MAAM,gBAAgB;IAC3B,QAAQ,IACN,IAAI,GAAG,CAAC;IACV,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,eAAe;AAC5B;AAGO,MAAM,eAAe;IAC1B,QAAQ,IACN,IAAI,GAAG,CAAC;IACV,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,cAAc;AAC3B;AAGO,MAAM,gBAAgB;IAC3B,QAAQ,IACN,IAAI,GAAG,CAAC;IACV,QAAQ,CAAC,OACP,IAAI,IAAI,CAAC,eAAe;AAC5B;uCAEe", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/designproject/web-dashboard/src/app/dashboard/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Plus, Search, Filter, Eye, Edit, Trash2, Package } from 'lucide-react';\nimport PageHeader from '@/components/ui/PageHeader';\nimport { productsApi } from '@/lib/api';\n\ninterface Product {\n  id: string;\n  sku: string;\n  name: string;\n  description: string;\n  price: number;\n  cost: number;\n  category: {\n    id: string;\n    name: string;\n  };\n  supplier: {\n    id: string;\n    name: string;\n  };\n  isActive: boolean;\n  createdAt: string;\n}\n\nexport default function ProductsPage() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [error, setError] = useState('');\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await productsApi.getAll({ search: searchTerm });\n      setProducts(response.data.products || []);\n    } catch (err: any) {\n      setError(err.response?.data?.error || 'Failed to fetch products');\n      console.error('Error fetching products:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      if (searchTerm !== '') {\n        fetchProducts();\n      }\n    }, 500);\n\n    return () => clearTimeout(timeoutId);\n  }, [searchTerm]);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Products</h1>\n          <p className=\"text-gray-600\">Manage your product catalog</p>\n        </div>\n        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\">\n          <Plus className=\"h-4 w-4\" />\n          <span>Add Product</span>\n        </button>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1 relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search products...\"\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n            <button className=\"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2\">\n              <Filter className=\"h-4 w-4\" />\n              <span>Filter</span>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Product\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  SKU\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Category\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Price\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Cost\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Status\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {products.map((product) => (\n                <tr key={product.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0 h-10 w-10\">\n                        <div className=\"h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center\">\n                          <Package className=\"h-5 w-5 text-gray-500\" />\n                        </div>\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">{product.name}</div>\n                        <div className=\"text-sm text-gray-500\">{product.description}</div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.sku}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.category}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    ${product.price.toFixed(2)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    ${product.cost.toFixed(2)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                      product.isActive \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {product.isActive ? 'Active' : 'Inactive'}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <button className=\"text-blue-600 hover:text-blue-900\">\n                        <Eye className=\"h-4 w-4\" />\n                      </button>\n                      <button className=\"text-green-600 hover:text-green-900\">\n                        <Edit className=\"h-4 w-4\" />\n                      </button>\n                      <button className=\"text-red-600 hover:text-red-900\">\n                        <Trash2 className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AALA;;;;AA0Be,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBAAE,QAAQ;YAAW;YAC/D,YAAY,SAAS,IAAI,CAAC,QAAQ,IAAI,EAAE;QAC1C,EAAE,OAAO,KAAU;gBACR,oBAAA;YAAT,SAAS,EAAA,gBAAA,IAAI,QAAQ,cAAZ,qCAAA,qBAAA,cAAc,IAAI,cAAlB,yCAAA,mBAAoB,KAAK,KAAI;YACtC,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,YAAY;oDAAW;oBAC3B,IAAI,eAAe,IAAI;wBACrB;oBACF;gBACF;mDAAG;YAEH;0CAAO,IAAM,aAAa;;QAC5B;iCAAG;QAAC;KAAW;IAEf,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAIV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAGjD,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;;;;;;;;;;;;8CAKnG,6LAAC;oCAAM,WAAU;8CACd,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;;;;;;;;;;;0EAGvB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAqC,QAAQ,IAAI;;;;;;kFAChE,6LAAC;wEAAI,WAAU;kFAAyB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;8DAIjE,6LAAC;oDAAG,WAAU;8DACX,QAAQ,GAAG;;;;;;8DAEd,6LAAC;oDAAG,WAAU;8DACX,QAAQ,QAAQ;;;;;;8DAEnB,6LAAC;oDAAG,WAAU;;wDAAoD;wDAC9D,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;8DAE1B,6LAAC;oDAAG,WAAU;;wDAAoD;wDAC9D,QAAQ,IAAI,CAAC,OAAO,CAAC;;;;;;;8DAEzB,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,AAAC,4DAIjB,OAHC,QAAQ,QAAQ,GACZ,gCACA;kEAEH,QAAQ,QAAQ,GAAG,WAAW;;;;;;;;;;;8DAGnC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;0EAEjB,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC,8MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CA5CjB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDnC;GAhKwB;KAAA", "debugId": null}}]}