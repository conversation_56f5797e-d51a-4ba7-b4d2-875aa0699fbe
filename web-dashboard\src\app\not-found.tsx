'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft, Home, Search } from 'lucide-react';

export default function NotFound() {
  const router = useRouter();

  const handleGoBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push('/dashboard');
    }
  };

  const handleGoHome = () => {
    router.push('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          {/* 404 Illustration */}
          <div className="mx-auto h-32 w-32 text-gray-300 mb-8">
            <svg
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              className="w-full h-full"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.467-.881-6.08-2.33M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
              />
            </svg>
          </div>

          {/* Error Message */}
          <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-gray-700 mb-2">Page Not Found</h2>
          <p className="text-gray-500 mb-8 max-w-md mx-auto">
            Sorry, we couldn't find the page you're looking for. The page might have been moved, deleted, or you might have entered the wrong URL.
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button
              onClick={handleGoBack}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </button>
            
            <button
              onClick={handleGoHome}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm bg-blue-600 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <Home className="h-4 w-4 mr-2" />
              Go to Dashboard
            </button>
          </div>

          {/* Search Suggestion */}
          <div className="mt-8 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-center text-gray-500 mb-2">
              <Search className="h-4 w-4 mr-2" />
              <span className="text-sm">Looking for something specific?</span>
            </div>
            <div className="text-sm text-gray-600">
              <p>Try navigating to:</p>
              <ul className="mt-2 space-y-1">
                <li>
                  <button
                    onClick={() => router.push('/dashboard')}
                    className="text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    Dashboard
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => router.push('/dashboard/inventory')}
                    className="text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    Inventory Management
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => router.push('/dashboard/orders')}
                    className="text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    Orders
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => router.push('/dashboard/products')}
                    className="text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    Products
                  </button>
                </li>
              </ul>
            </div>
          </div>

          {/* Help Text */}
          <div className="mt-6 text-xs text-gray-400">
            <p>Error Code: 404 | Page Not Found</p>
            <p>If you believe this is an error, please contact support.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
